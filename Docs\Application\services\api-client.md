# API Client Service

Rate Limit: 10 req/sec/ip [api](../../Bitunix/common/introduction.md#rate-limits)

### Description [src](../../src/services/BitunixApiClient.ts)

REST API client for Bitunix exchange with authentication, rate limiting, and error handling.

### Configuration [src](../../src/config/config.ts#BitunixConfig)

| Parameter | Type | Required | Description |
| --- | --- | --- | --- |
| apiKey | string | Yes | Bitunix API key [api](../../Bitunix/common/introduction.md) |
| secretKey | string | Yes | Bitunix secret key [api](../../Bitunix/common/introduction.md) |
| baseUrl | string | No | API base URL (default: https://fapi.bitunix.com) |
| timeout | number | No | Request timeout (default: 10000ms) |

Usage Example:

```typescript
const apiClient = new BitunixApiClient({
  apiKey: process.env.BITUNIX_API_KEY!,
  secretKey: process.env.BITUNIX_SECRET_KEY!,
  baseUrl: 'https://fapi.bitunix.com',
  timeout: 10000,
});

// Get order book depth
const depthResponse = await apiClient.getDepth('ETHUSDT', '50');

// Get kline data
const klineResponse = await apiClient.getKline('ETHUSDT', '1m', undefined, undefined, 100);
```

### API Methods [src](../../src/services/BitunixApiClient.ts#methods)

| Method | Parameters | Returns | Description |
| --- | --- | --- | --- |
| getDepth | symbol, limit | DepthResponse | Order book depth [api](../../Bitunix/market/get-depth.md) |
| getKline | symbol, interval, startTime?, endTime?, limit? | KlineResponse | Candlestick data [api](../../Bitunix/market/get-kline.md) |
| getTickers | symbols? | TickersResponse | Market tickers [api](../../Bitunix/market/get-tickers.md) |

### Authentication [src](../../src/services/BitunixApiClient.ts#auth)

| Header | Description |
| --- | --- |
| api-key | Bitunix API key |
| nonce | 32-bit random string |
| timestamp | Request timestamp in milliseconds |
| sign | Double SHA256 signature [api](../../Bitunix/common/sign.md) |

### Rate Limiting [src](../../src/services/BitunixApiClient.ts#rateLimiter)

| Feature | Value | Description |
| --- | --- | --- |
| Requests per second | 9/10 | Conservative limit with buffer |
| Burst allowance | 5 | Short burst capacity |
| Queue management | FIFO | Request queuing when limit reached |
| Backoff strategy | Exponential | Automatic retry with backoff |

### Error Handling [src](../../src/services/BitunixApiClient.ts#errorHandling)

| Error Type | Response | Description |
| --- | --- | --- |
| Rate limit exceeded | 429 | Automatic retry with backoff |
| Authentication failed | 401 | Invalid API credentials |
| Invalid parameters | 400 | Request validation error |
| Network timeout | Timeout | Connection or response timeout |
