# Storage System

### Description [src](../../src/services/DataStorage.ts)

Efficient JSON Lines data persistence with automatic file rotation and compression for market data storage.

### Configuration [src](../../src/config/config.ts#StorageConfig)

| Parameter | Type | Required | Description |
| --- | --- | --- | --- |
| storageDir | string | Yes | Data storage directory (default: ./data) |
| maxFileSize | number | No | Max file size before rotation (default: 50MB) |
| compressionEnabled | boolean | No | Enable gzip compression (default: true) |
| retentionDays | number | No | Data retention period (default: 30 days) |

Usage Example:

```typescript
const storage = new DataStorage('./data', {
  maxFileSize: 50 * 1024 * 1024, // 50MB
  compressionEnabled: true,
  retentionDays: 30,
});

// Store different data types
await storage.storeDepthData('ETHUSDT', depthData, '50');
await storage.storeKlineData('ETHUSDT', '1m', klineData);
await storage.storeTradeData('ETHUSDT', tradeData);
```

### Storage Methods [src](../../src/services/DataStorage.ts#methods)

| Method | Parameters | Description |
| --- | --- | --- |
| storeDepthData | symbol, data, limit | Store order book depth data |
| storeKlineData | symbol, interval, data | Store candlestick data |
| storeTradeData | symbol, data | Store trade execution data |
| storeMarketPriceData | symbol, data | Store real-time price data |

### File Structure [src](../../src/services/DataStorage.ts#fileStructure)

```
data/
├── depth/
│   └── ETHUSDT_depth_50_20231201.jsonl.gz
├── kline/
│   ├── ETHUSDT_kline_1m_20231201.jsonl.gz
│   ├── ETHUSDT_kline_3m_20231201.jsonl.gz
│   └── ETHUSDT_kline_5m_20231201.jsonl.gz
├── trades/
│   └── ETHUSDT_trades_20231201.jsonl.gz
└── market-price/
    └── ETHUSDT_price_20231201.jsonl.gz
```

### Data Formats [→](../data-formats/storage.md)

| File Type | Format | Description |
| --- | --- | --- |
| Depth | JSON Lines | Order book snapshots with timestamp |
| Kline | JSON Lines | OHLCV data with interval metadata |
| Trades | JSON Lines | Individual trade executions |
| Market Price | JSON Lines | Real-time price updates |

### File Management [src](../../src/services/DataStorage.ts#fileManagement)

| Feature | Description |
| --- | --- |
| Automatic Rotation | New file when size limit reached |
| Compression | Gzip compression for storage efficiency |
| Cleanup | Automatic deletion of old files |
| Directory Structure | Organized by data type and symbol |

### Performance Optimization [src](../../src/services/DataStorage.ts#optimization)

| Feature | Value | Description |
| --- | --- | --- |
| Batch Writing | 100 records | Batch writes for efficiency |
| Buffer Management | 1MB | Write buffer size |
| Async Operations | Non-blocking | Asynchronous file operations |
| Memory Usage | <10MB | Efficient memory management |
