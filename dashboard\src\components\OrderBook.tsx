'use client'

import React, { useState, useEffect } from 'react'
import { OrderBookData, OrderBookLevel } from '@/types/market'
import { format } from 'date-fns'

interface OrderBookProps {
  data: OrderBookData | null
  isLoading?: boolean
}

interface OrderBookRowProps {
  level: OrderBookLevel
  maxTotal: number
  type: 'bid' | 'ask'
  isFlashing?: boolean
}

function OrderBookRow({ level, maxTotal, type, isFlashing }: OrderBookRowProps) {
  const percentage = (level.total / maxTotal) * 100
  const bgColor = type === 'bid' ? 'bg-success-50' : 'bg-danger-50'
  const textColor = type === 'bid' ? 'text-success-700' : 'text-danger-700'
  
  return (
    <div 
      className={`relative flex justify-between items-center py-1 px-2 text-sm transition-all duration-200 ${
        isFlashing ? (type === 'bid' ? 'flash-green' : 'flash-red') : ''
      }`}
    >
      {/* Background bar showing depth */}
      <div 
        className={`absolute inset-0 ${bgColor} opacity-30`}
        style={{ width: `${percentage}%` }}
      />
      
      {/* Content */}
      <div className="relative z-10 flex justify-between w-full">
        <span className={`font-mono ${textColor}`}>
          ${level.price.toFixed(2)}
        </span>
        <span className="text-gray-600 font-mono">
          {level.size.toFixed(4)}
        </span>
        <span className="text-gray-500 font-mono text-xs">
          {level.total.toFixed(4)}
        </span>
      </div>
    </div>
  )
}

export default function OrderBook({ data, isLoading = false }: OrderBookProps) {
  const [prevData, setPrevData] = useState<OrderBookData | null>(null)
  const [flashingLevels, setFlashingLevels] = useState<Set<string>>(new Set())
  
  useEffect(() => {
    if (data && prevData) {
      const newFlashing = new Set<string>()
      
      data.bids.slice(0, 10).forEach((bid, index) => {
        const prevBid = prevData.bids[index]
        if (prevBid && (prevBid.price !== bid.price || prevBid.size !== bid.size)) {
          newFlashing.add(`bid-${index}`)
        }
      })
      
      data.asks.slice(0, 10).forEach((ask, index) => {
        const prevAsk = prevData.asks[index]
        if (prevAsk && (prevAsk.price !== ask.price || prevAsk.size !== ask.size)) {
          newFlashing.add(`ask-${index}`)
        }
      })
      
      setFlashingLevels(newFlashing)
      setTimeout(() => setFlashingLevels(new Set()), 500)
    }
    
    setPrevData(data)
  }, [data, prevData])

  if (!data) {
    return (
      <div className="card">
        <div className="card-header">
          <h3 className="card-title">Order Book</h3>
          <span className="text-sm text-gray-500">No data</span>
        </div>
        <div className="flex items-center justify-center h-96 text-gray-500">
          <div className="text-center">
            <div className="animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-32 mx-auto mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-24 mx-auto"></div>
            </div>
            <p className="mt-4">Loading order book...</p>
          </div>
        </div>
      </div>
    )
  }

  const maxTotal = Math.max(
    ...data.bids.slice(0, 10).map(b => b.total),
    ...data.asks.slice(0, 10).map(a => a.total)
  )

  const spread = data.asks.length > 0 && data.bids.length > 0 
    ? data.asks[0].price - data.bids[0].price 
    : 0
  
  const spreadPercent = data.bids.length > 0 
    ? (spread / data.bids[0].price) * 100 
    : 0

  return (
    <div className="card">
      <div className="card-header">
        <h3 className="card-title">Order Book</h3>
        <div className="text-right">
          <div className="text-sm text-gray-500">
            Last update: {format(new Date(data.timestamp), 'HH:mm:ss')}
          </div>
          {spread > 0 && (
            <div className="text-sm text-gray-600">
              Spread: ${spread.toFixed(2)} ({spreadPercent.toFixed(3)}%)
            </div>
          )}
        </div>
      </div>

      {isLoading && (
        <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
        </div>
      )}

      <div className="space-y-4">
        {/* Header */}
        <div className="flex justify-between text-xs font-medium text-gray-500 uppercase tracking-wide px-2">
          <span>Price (USDT)</span>
          <span>Size (ETH)</span>
          <span>Total (ETH)</span>
        </div>

        {/* Asks (Sell orders) */}
        <div className="space-y-0.5">
          <div className="text-xs font-medium text-danger-600 mb-2 px-2">
            Asks (Sell)
          </div>
          {data.asks.slice(0, 10).reverse().map((ask, index) => (
            <OrderBookRow
              key={`ask-${ask.price}-${index}`}
              level={ask}
              maxTotal={maxTotal}
              type="ask"
              isFlashing={flashingLevels.has(`ask-${9 - index}`)}
            />
          ))}
        </div>

        {/* Spread indicator */}
        {spread > 0 && (
          <div className="flex justify-center py-2 border-t border-b border-gray-200">
            <div className="text-center">
              <div className="text-lg font-bold text-success-600">
                ${data.bids[0]?.price.toFixed(2)}
              </div>
              <div className="text-xs text-gray-500">Best Bid</div>
              <div className="text-lg font-bold text-danger-600 mt-1">
                ${data.asks[0]?.price.toFixed(2)}
              </div>
              <div className="text-xs text-gray-500">Best Ask</div>
            </div>
          </div>
        )}

        {/* Bids (Buy orders) */}
        <div className="space-y-0.5">
          <div className="text-xs font-medium text-success-600 mb-2 px-2">
            Bids (Buy)
          </div>
          {data.bids.slice(0, 10).map((bid, index) => (
            <OrderBookRow
              key={`bid-${bid.price}-${index}`}
              level={bid}
              maxTotal={maxTotal}
              type="bid"
              isFlashing={flashingLevels.has(`bid-${index}`)}
            />
          ))}
        </div>
      </div>
    </div>
  )
}
