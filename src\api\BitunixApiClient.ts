import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { logger } from '@/utils/logger';
import { generateSignatureHeaders } from '@/utils/signature';
import { 
  BitunixConfig, 
  RateLimitInfo,
  DepthResponse,
  DepthResponseSchema,
  KlineResponse,
  KlineResponseSchema,
  KlineInterval,
  KlineType,
  DepthLimit
} from '@/types/bitunix';

export class BitunixApiClient {
  private readonly client: AxiosInstance;
  private readonly config: BitunixConfig;
  private rateLimitInfo: RateLimitInfo = {
    requestsRemaining: 10,
    resetTime: Date.now() + 1000,
  };

  constructor(config: BitunixConfig) {
    this.config = config;
    
    this.client = axios.create({
      baseURL: config.baseUrl,
      timeout: 10000,
    });

    this.setupInterceptors();
  }

  private setupInterceptors(): void {
    this.client.interceptors.request.use(
      (config) => {
        const headers = generateSignatureHeaders(
          this.config.apiKey,
          this.config.secretKey,
          config.params || {},
          config.data
        );
        
        Object.assign(config.headers, headers);
        
        logger.debug('API Request', {
          url: config.url,
          method: config.method,
          params: config.params,
        });
        
        return config;
      },
      (error) => {
        logger.error('Request interceptor error:', error);
        return Promise.reject(error);
      }
    );

    this.client.interceptors.response.use(
      (response) => {
        this.updateRateLimitInfo(response);
        
        logger.debug('API Response', {
          url: response.config.url,
          status: response.status,
          data: response.data,
        });
        
        return response;
      },
      (error) => {
        logger.error('API request failed:', {
          url: error.config?.url,
          method: error.config?.method,
          status: error.response?.status,
          data: error.response?.data,
        });
        return Promise.reject(error);
      }
    );
  }

  private updateRateLimitInfo(response: AxiosResponse): void {
    const remaining = response.headers['x-ratelimit-remaining'];
    const reset = response.headers['x-ratelimit-reset'];

    if (remaining) {
      this.rateLimitInfo.requestsRemaining = parseInt(remaining, 10);
    }
    if (reset) {
      this.rateLimitInfo.resetTime = parseInt(reset, 10) * 1000;
    }
  }

  public getRateLimitInfo(): RateLimitInfo {
    return { ...this.rateLimitInfo };
  }

  public async getDepth(symbol: string, limit: DepthLimit = '50'): Promise<DepthResponse> {
    const response = await this.client.get('/api/v1/futures/market/depth', {
      params: { symbol, limit }
    });
    
    return DepthResponseSchema.parse(response.data);
  }

  public async getKline(
    symbol: string,
    interval: KlineInterval,
    startTime?: number,
    endTime?: number,
    limit: number = 100,
    type: KlineType = 'LAST_PRICE'
  ): Promise<KlineResponse> {
    const params: Record<string, string | number> = {
      symbol,
      interval,
      limit,
      type,
    };
    
    if (startTime) params['startTime'] = startTime;
    if (endTime) params['endTime'] = endTime;

    const response = await this.client.get('/api/v1/futures/market/kline', { params });
    
    return KlineResponseSchema.parse(response.data);
  }

  public async ping(): Promise<boolean> {
    try {
      // Test connectivity with a simple depth request
      await this.client.get('/api/v1/futures/market/depth', {
        params: { symbol: 'ETHUSDT', limit: '1' }
      });
      return true;
    } catch {
      return false;
    }
  }
}
