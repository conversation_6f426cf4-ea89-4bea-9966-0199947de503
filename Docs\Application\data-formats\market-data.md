# Market Data Types

## Overview

This document defines the TypeScript interfaces and data structures used throughout the Bitunix Trading Application for market data representation, validation, and processing.

## Core Data Interfaces

### CandlestickData
Represents OHLCV (Open, High, Low, Close, Volume) data for price charts and analysis.

```typescript
interface CandlestickData {
  x: number;        // Unix timestamp in milliseconds
  o: number;        // Open price
  h: number;        // High price
  l: number;        // Low price
  c: number;        // Close price
  volume: number;   // Trading volume
}
```

**Usage**: Price charts, CVD calculations, market analysis
**Source**: Transformed from Bitunix kline API responses

### OrderBookData
Represents order book depth with bid and ask levels.

```typescript
interface OrderBookLevel {
  price: number;    // Price level
  size: number;     // Volume at this level
  total: number;    // Cumulative volume from best price
}

interface OrderBookData {
  bids: OrderBookLevel[];  // Buy orders (highest price first)
  asks: OrderBookLevel[];  // Sell orders (lowest price first)
}
```

**Usage**: Order book visualization, spread calculation, liquidity analysis
**Source**: Transformed from Bitunix depth API responses

### Trade
Represents individual trade executions.

```typescript
interface Trade {
  id: string;           // Unique trade identifier
  price: number;        // Execution price
  volume: number;       // Trade volume
  side: 'buy' | 'sell'; // Trade direction
  timestamp: number;    // Execution timestamp
}
```

**Usage**: Trade feed, CVD calculations, market activity analysis
**Source**: WebSocket trade channel and REST API responses

### MarketStats
Aggregated market statistics and metrics.

```typescript
interface MarketStats {
  currentPrice: number;      // Latest price
  change24h: number;         // 24-hour price change
  changePercent24h: number;  // 24-hour percentage change
  volume24h: number;         // 24-hour trading volume
  high24h: number;          // 24-hour high price
  low24h: number;           // 24-hour low price
  spread: number;           // Current bid-ask spread
  lastUpdate: number;       // Last update timestamp
}
```

**Usage**: Market statistics panel, dashboard header
**Source**: Calculated from collected market data

## Timeframe Types

### TimeFrame
Supported chart timeframes for data aggregation.

```typescript
type TimeFrame = '1m' | '3m' | '5m' | '15m';
```

**Usage**: Chart timeframe selection, data filtering, CVD synchronization
**Mapping**: 
- `1m` = 60,000ms
- `3m` = 180,000ms  
- `5m` = 300,000ms
- `15m` = 900,000ms

## Dashboard State Management

### DashboardState
Central state management for dashboard components.

### Validation [src](../../src/validation/schemas.ts)

| Schema | Description |
| --- | --- |
| CandlestickDataSchema | Validates OHLCV data structure |
| OrderBookDataSchema | Validates order book format |
| TradeSchema | Validates trade execution data |
| MarketStatsSchema | Validates market statistics |

### Constants [src](../../src/constants/market.ts)

| Constant | Value | Description |
| --- | --- | --- |
| TIMEFRAMES | ['1m', '3m', '5m', '15m'] | Supported chart timeframes |
| TIMEFRAME_MS | {1m: 60000, 3m: 180000, ...} | Timeframe to milliseconds mapping |
| DEFAULT_DEPTH_LIMIT | 50 | Default order book depth |
| MAX_TRADE_HISTORY | 100 | Maximum trade history length |
