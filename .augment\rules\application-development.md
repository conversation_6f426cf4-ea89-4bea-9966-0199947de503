---
type: "agent_requested"
description: "Application development and performance rules"
---
# Application Development Rules

### Description

Rules for developing and maintaining the Bitunix Trading Application with performance targets and component integration.

### Development Priority Framework [src](../../src/)

| Priority | Component | Validation |
| --- | --- | --- |
| 1 | Data Collection [→](../../Docs/Application/services/data-collector.md) | API utilization ≤18% ✓/✗ |
| 2 | Real-time Distribution [→](../../Docs/Application/dashboard/websocket-server.md) | Sub-second latency ✓/✗ |
| 3 | Data Visualization [→](../../Docs/Application/components/) | 60fps rendering ✓/✗ |
| 4 | Error Recovery | Graceful degradation ✓/✗ |
| 5 | Performance Monitoring | Resource usage tracking ✓/✗ |

### Component Integration Decision Tree

```
Component Development:
├── Data Component → Implement data validation first
├── UI Component → Implement props interface first
├── Service Component → Implement error handling first
└── Utility Component → Implement type safety first

Data Flow Implementation:
├── Collection → Validation → Storage → Processing → Distribution
├── WebSocket → Event handling → State management → UI updates
├── User input → Validation → State update → Component re-render
└── Error → Logging → Recovery → User notification
```

### Performance Optimization Rules

| Metric | Target | Monitoring | Action if Exceeded |
| --- | --- | --- | --- |
| API utilization | <20% of rate limit | Request counter | Reduce collection frequency |
| Memory usage | <100MB per service | Process monitoring | Implement data cleanup |
| WebSocket latency | <100ms | Ping/pong timing | Optimize message processing |
| Chart rendering | 60fps | Frame rate monitoring | Reduce data points |
| File I/O | <10MB/sec | Disk usage monitoring | Implement batching |

### Component Development Protocol

| Component Type | Required Elements | Validation |
| --- | --- | --- |
| React Component | Props interface, error boundary | TypeScript compilation ✓/✗ |
| Service Class | Error handling, event emission | Unit tests passing ✓/✗ |
| Data Processor | Input validation, output formatting | Integration tests ✓/✗ |
| Configuration | Environment validation, defaults | Config loading ✓/✗ |

### Error Handling Strategy

| Error Level | Response | Recovery |
| --- | --- | --- |
| Critical | Stop service, alert user | Manual intervention required |
| High | Degrade functionality, continue | Automatic retry with backoff |
| Medium | Log error, continue operation | Background recovery attempt |
| Low | Log warning, continue | No action required |

### State Management Rules

| State Type | Storage | Persistence | Validation |
| --- | --- | --- | --- |
| WebSocket connection | React Context | Session-based | Connection status ✓/✗ |
| Market data | Component state | Real-time updates | Data freshness ✓/✗ |
| User preferences | Local storage | Persistent | Schema validation ✓/✗ |
| Application config | Environment variables | Static | Required fields ✓/✗ |

### Testing Requirements

| Test Type | Coverage | Criteria | Automation |
| --- | --- | --- | --- |
| Unit tests | >80% code coverage | All business logic | CI/CD pipeline |
| Integration tests | API interactions | All external dependencies | Pre-deployment |
| Performance tests | Critical paths | Response time targets | Load testing |
| E2E tests | User workflows | Complete user journeys | Staging environment |

### Code Quality Standards

| Standard | Requirement | Check |
| --- | --- | --- |
| TypeScript | Strict mode enabled | No compilation errors |
| ESLint | No warnings/errors | Clean linting |
| Prettier | Consistent formatting | Auto-format on save |

### Deployment Protocol

| Environment | Requirements | Validation |
| --- | --- | --- |
| Development | Hot reloading, debug logging | Local functionality ✓/✗ |
| Staging | Production-like, test data | Integration tests ✓/✗ |
| Production | Optimized build, monitoring | Performance benchmarks ✓/✗ |

### Change Impact Analysis

| Change Type | Impact Assessment | Required Updates |
| --- | --- | --- |
| API endpoint change | Check all API client usage | Update client, tests, docs |
| Data format change | Check all data processors | Update schemas, validation |
| Component interface | Check all component usage | Update props, documentation |
| Configuration change | Check all config consumers | Update validation, defaults |

### Resource Management Rules

| Resource | Limit | Monitoring | Cleanup |
| --- | --- | --- | --- |
| WebSocket connections | 1 per client | Connection count | Auto-disconnect on idle |
| File handles | <100 open files | File descriptor count | Close after processing |
| Memory buffers | <50MB per buffer | Memory usage tracking | Periodic cleanup |
| Event listeners | Remove on unmount | Listener registry | Component cleanup |

### Security Implementation

| Security Aspect | Implementation | Validation |
| --- | --- | --- |
| API credentials | Environment variables only | No hardcoded secrets ✓/✗ |
| Input validation | Zod schemas | All inputs validated ✓/✗ |
| Error messages | No sensitive data exposure | Error message review ✓/✗ |
| File permissions | Restricted access | Permission audit ✓/✗ |
