# Bitunix API Configuration
BITUNIX_API_BASE_URL=https://fapi.bitunix.com
BITUNIX_WS_URL=wss://fapi.bitunix.com/public/
BITUNIX_API_KEY=your_api_key_here
BITUNIX_SECRET_KEY=your_secret_key_here

# Application Configuration
NODE_ENV=development
LOG_LEVEL=info
DATA_COLLECTION_INTERVAL=60000

# Data Collection Configuration
TRADING_SYMBOL=ETHUSDT
KLINE_INTERVALS=1m,3m,5m,15m
DEPTH_LIMIT=50
ENABLE_WEBSOCKET=true
ENABLE_REST_API=true
STORAGE_DIR=./data

# Rate Limiting (10 req/sec/ip as per Bitunix documentation)
RATE_LIMIT_REQUESTS_PER_SECOND=10
RATE_LIMIT_BURST=5
