---
type: "agent_requested"
description: "Error handling and recovery protocols"
---
# Error Handling Rules

### Description

Rules for handling errors, implementing recovery protocols, and maintaining system stability in the Bitunix Trading Application.

### Error Classification

| Level | Criteria | Response | Recovery |
| --- | --- | --- | --- |
| Critical | Service stops | Stop service, alert | Manual restart |
| High | Feature fails | Degrade gracefully | Auto retry |
| Medium | Data error | Log and continue | Background fix |
| Low | Warning condition | Log only | No action |

### API Error Handling

| HTTP Status | Error Type | Action | Retry |
| --- | --- | --- | --- |
| 401 | Authentication | Check credentials | No |
| 429 | Rate limit | Wait and retry | Yes, with backoff |
| 400 | Bad request | Fix parameters | No |
| 500 | Server error | Retry with backoff | Yes, max 3 times |
| Timeout | Network | Retry immediately | Yes, max 2 times |

### WebSocket Error Handling

| Error | Detection | Action | Recovery |
| --- | --- | --- | --- |
| Connection lost | Socket close event | Reconnect with backoff | Auto |
| Invalid message | Parse failure | Log and skip | Continue |
| Subscription failed | Error response | Resubscribe | Auto |
| Rate limit | Too many messages | Throttle messages | Auto |

### Data Error Handling

| Error Type | Detection | Action | Recovery |
| --- | --- | --- | --- |
| Invalid JSON | Parse exception | Skip record | Continue |
| Missing fields | Schema validation | Use defaults | Continue |
| Invalid types | Type check | Convert or skip | Continue |
| Corrupted file | Read failure | Use backup | Manual |

### Recovery Protocols

| Component | Failure | Recovery Method | Timeout |
| --- | --- | --- | --- |
| API Client | Connection lost | Exponential backoff | 30 seconds |
| WebSocket | Disconnect | Immediate reconnect | 5 seconds |
| File System | Write failure | Retry with new file | 10 seconds |
| Database | Query failure | Retry with backoff | 15 seconds |

### Circuit Breaker Rules

| Service | Failure Threshold | Timeout | Recovery Test |
| --- | --- | --- | --- |
| API calls | 5 failures in 1 minute | 30 seconds | Single test call |
| File operations | 3 failures in 30 seconds | 10 seconds | Test write |
| WebSocket | 3 disconnects in 1 minute | 60 seconds | Test connection |

### Logging Rules

| Level | Criteria | Include | Retention |
| --- | --- | --- | --- |
| ERROR | All errors | Stack trace, context | 7 days |
| WARN | Recoverable issues | Error details | 3 days |
| INFO | Normal operations | Key events | 1 day |
| DEBUG | Development | All details | Session only |

### Alert Rules

| Condition | Threshold | Action | Escalation |
| --- | --- | --- | --- |
| Error rate | >1% in 5 minutes | Log alert | Email after 10 minutes |
| Service down | >30 seconds | Immediate alert | SMS after 1 minute |
| Memory leak | >200MB growth | Monitor alert | Alert after 5 minutes |
| Disk full | >90% usage | Warning alert | Critical at 95% |
