# Application Documentation

### Description

Complete documentation for the Bitunix Trading Application - real-time cryptocurrency data collection and visualization system.

### Documentation Structure

| Category | Files | Description |
| --- | --- | --- |
| Getting Started | [Introduction](./introduction.md), [Configuration](./configuration.md) | Overview and setup |
| Core Services | [Data Collector](./services/data-collector.md) | Backend data collection |
| Dashboard | [WebSocket Server](./dashboard/websocket-server.md) | Real-time data distribution |
| Components | [CVD Component](./components/cvd.md) | UI visualization components |
| Development | [Scripts Guide](./development/scripts.md) | Development workflows |
| Data Formats | [Market Data Types](./data-formats/market-data.md) | TypeScript interfaces |

### Quick Navigation

| For | Start Here | Then |
| --- | --- | --- |
| New Developers | [Introduction](./introduction.md) | [Configuration](./configuration.md) → [Scripts](./development/scripts.md) |
| System Integration | [Data Collector](./services/data-collector.md) | [Market Data Types](./data-formats/market-data.md) |
| Frontend Development | [WebSocket Server](./dashboard/websocket-server.md) | [CVD Component](./components/cvd.md) |
| Operations | [Configuration](./configuration.md) | [Scripts Guide](./development/scripts.md) |

### Architecture Overview

```
Bitunix API → Data Collector → Storage → WebSocket Server → Dashboard
     ↓              ↓            ↓           ↓              ↓
  REST/WS      Validation    JSON Files   Real-time     Visualization
```

### Key Features

| Feature | Component | Description |
| --- | --- | --- |
| High-Frequency Collection | [Data Collector](./services/data-collector.md) | 1s order book, 5s kline updates |
| Real-Time Visualization | [CVD Component](./components/cvd.md) | Cumulative Volume Delta analysis |
| WebSocket Distribution | [WebSocket Server](./dashboard/websocket-server.md) | Live data streaming |
| Multiple Timeframes | [Market Data Types](./data-formats/market-data.md) | 1m, 3m, 5m, 15m synchronization |

### Service URLs

| Service | URL | Documentation |
| --- | --- | --- |
| Dashboard | http://localhost:3001 | [WebSocket Server](./dashboard/websocket-server.md) |
| WebSocket Server | ws://localhost:8080 | [WebSocket Server](./dashboard/websocket-server.md) |
| Data Storage | ./data/ | [Data Collector](./services/data-collector.md) |

### Documentation Standards

This documentation follows Bitunix API standards:
- **Length**: 45-75 lines per file
- **Structure**: Description → Parameters → Example → Output
- **Cross-references**: Minimal overhead, maximum navigation value
- **Technical focus**: Specification over explanation

### Quick Start

```bash
# Setup
npm run app:install
cp .env.example .env.local
# Edit .env.local with Bitunix API credentials

# Development
npm run app:dev
```

For detailed setup: [Configuration Guide](./configuration.md) • [Scripts Guide](./development/scripts.md)
