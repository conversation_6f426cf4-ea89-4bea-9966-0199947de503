# Open Interest Component

### Description [src](../../dashboard/src/components/OpenInterest.tsx)

Market liquidity analysis component displaying open interest metrics and depth visualization.

### Props [src](../../dashboard/src/components/OpenInterest.tsx#OpenInterestProps)

| Parameter | Type | Required | Description |
| --- | --- | --- | --- |
| orderBookData | OrderBookData [→](../data-formats/market-data.md#orderbookdata) | Yes | Order book depth data |
| showLiquidityRatio | boolean | No | Show bid/ask liquidity ratio (default: true) |
| showDepthChart | boolean | No | Show depth visualization (default: true) |
| maxDepthLevels | number | No | Maximum depth levels to analyze (default: 50) |

Usage Example:

```typescript
<OpenInterest
  orderBookData={orderBookData}
  showLiquidityRatio={true}
  showDepthChart={true}
  maxDepthLevels={50}
/>
```

### Liquidity Metrics [src](../../dashboard/src/components/OpenInterest.tsx#metrics)

| Metric | Calculation | Description |
| --- | --- | --- |
| Total Liquidity | Σ(Bid Volume × Price) + Σ(Ask Volume × Price) | Total USD value in order book |
| Bid Liquidity | Σ(Bid Volume × Price) | USD value of buy orders |
| Ask Liquidity | Σ(Ask Volume × Price) | USD value of sell orders |
| Liquidity Ratio | Bid Liquidity / Ask Liquidity | Market balance indicator |
| Average Spread | Σ(Ask Price - Bid Price) / Levels | Average spread across levels |

### Depth Visualization [src](../../dashboard/src/components/OpenInterest.tsx#visualization)

```typescript
const depthChartConfig = {
  type: 'line',
  data: {
    datasets: [
      {
        label: 'Bid Depth',
        data: bidDepthData,
        borderColor: '#10b981',
        backgroundColor: 'rgba(16, 185, 129, 0.1)',
        fill: true,
      },
      {
        label: 'Ask Depth',
        data: askDepthData,
        borderColor: '#ef4444',
        backgroundColor: 'rgba(239, 68, 68, 0.1)',
        fill: true,
      },
    ],
  },
};
```

### Market Depth Analysis [src](../../dashboard/src/components/OpenInterest.tsx#analysis)

| Analysis | Description |
| --- | --- |
| Support Levels | Strong bid clusters indicating price support |
| Resistance Levels | Strong ask clusters indicating price resistance |
| Market Imbalance | Significant difference between bid/ask liquidity |
| Liquidity Gaps | Price levels with minimal order book depth |

### Real-Time Updates [src](../../dashboard/src/components/OpenInterest.tsx#updates)

| Feature | Update Frequency | Description |
| --- | --- | --- |
| Liquidity Metrics | 1 second | Real-time calculation from order book |
| Depth Chart | 1 second | Visual depth representation |
| Support/Resistance | 5 seconds | Level identification and analysis |
| Market Balance | 1 second | Bid/ask ratio monitoring |

### Display Format [src](../../dashboard/src/components/OpenInterest.tsx#display)

```
OPEN INTEREST & MARKET DEPTH
┌─────────────────────────────────────────┐
│ Total Liquidity: $1,234,567            │
│ Bid Liquidity:   $678,901 (55%)        │
│ Ask Liquidity:   $555,666 (45%)        │
│ Liquidity Ratio: 1.22 (Bid Favored)    │
│ Average Spread:  $0.25 (0.008%)        │
└─────────────────────────────────────────┘

[Depth Chart Visualization]
```

### Market Insights [src](../../dashboard/src/components/OpenInterest.tsx#insights)

| Insight | Condition | Interpretation |
| --- | --- | --- |
| Strong Support | High bid liquidity concentration | Price likely to hold above level |
| Strong Resistance | High ask liquidity concentration | Price likely to face resistance |
| Balanced Market | Liquidity ratio near 1.0 | Neutral market sentiment |
| Bid Imbalance | Liquidity ratio > 1.2 | Bullish market sentiment |
| Ask Imbalance | Liquidity ratio < 0.8 | Bearish market sentiment |

### Data Output [src](../../dashboard/src/components/OpenInterest.tsx#output)

| Field | Type | Description |
| --- | --- | --- |
| totalLiquidity | number | Total USD value in order book |
| bidLiquidity | number | USD value of bid side |
| askLiquidity | number | USD value of ask side |
| liquidityRatio | number | Bid/ask liquidity ratio |
| averageSpread | number | Average spread across levels |
| supportLevels | number[] | Identified support price levels |
| resistanceLevels | number[] | Identified resistance price levels |
