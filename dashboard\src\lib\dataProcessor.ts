import fs from 'fs/promises';
import path from 'path';
import chokidar from 'chokidar';
import { EventEmitter } from 'events';
import {
  DepthData,
  KlineData,
  MarketPriceData,
  CandlestickData,
  OrderBookData,
  Trade,
  MarketStats,
  TimeFrame,
  DepthDataSchema,
  KlineDataSchema,
} from '@/types/market';

export class DataProcessor extends EventEmitter {
  private dataDir: string;
  private watcher: chokidar.FSWatcher | null = null;
  private latestData: {
    depth: DepthData | null;
    kline: Map<TimeFrame, KlineData[]>;
    price: MarketPriceData | null;
    trades: Trade[];
  } = {
    depth: null,
    kline: new Map(),
    price: null,
    trades: [],
  };

  constructor(dataDir: string = '../data') {
    super();
    this.dataDir = path.resolve(dataDir);
    console.log('DataProcessor watching directory:', this.dataDir);
    this.initializeWatcher();
  }

  private initializeWatcher(): void {
    this.watcher = chokidar.watch(this.dataDir, {
      ignored: /(^|[\/\\])\../, // ignore dotfiles
      persistent: true,
      ignoreInitial: false,
    });

    this.watcher
      .on('add', (filePath) => {
        console.log('File added:', filePath);
        this.handleFileChange(filePath);
      })
      .on('change', (filePath) => {
        console.log('File changed:', filePath);
        this.handleFileChange(filePath);
      })
      .on('error', (error) => {
        console.error('File watcher error:', error);
        this.emit('error', error);
      });
  }

  private async handleFileChange(filePath: string): Promise<void> {
    try {
      const relativePath = path.relative(this.dataDir, filePath);
      const pathParts = relativePath.split(path.sep);
      
      if (pathParts.length < 2) return;
      
      const dataType = pathParts[0];
      const fileName = pathParts[1];
      
      const currentHour = new Date().getHours().toString().padStart(2, '0');
      if (!fileName.includes(`_${currentHour}.json`)) return;

      switch (dataType) {
        case 'depth':
          await this.processDepthFile(filePath);
          break;
        case 'kline':
          await this.processKlineFile(filePath, fileName);
          break;
        case 'market-price':
          await this.processPriceFile(filePath);
          break;
        case 'trades':
          await this.processTradeFile(filePath);
          break;
      }
    } catch (error) {
      this.emit('error', error);
    }
  }

  private async processDepthFile(filePath: string): Promise<void> {
    const content = await fs.readFile(filePath, 'utf-8');
    const lines = content.trim().split('\n').filter(line => line.trim());
    
    if (lines.length === 0) return;
    
    const latestLine = lines[lines.length - 1];
    const depthData = DepthDataSchema.parse(JSON.parse(latestLine));
    
    this.latestData.depth = depthData;
    this.emit('depth', this.transformDepthData(depthData));
  }

  private async processKlineFile(filePath: string, fileName: string): Promise<void> {
    const content = await fs.readFile(filePath, 'utf-8');
    const lines = content.trim().split('\n').filter(line => line.trim());
    
    if (lines.length === 0) return;
    
    const intervalMatch = fileName.match(/ETHUSDT_(\w+)_/);
    if (!intervalMatch) return;
    
    const interval = intervalMatch[1] as TimeFrame;
    const latestLine = lines[lines.length - 1];
    const klineArray = JSON.parse(latestLine);
    
    const validatedKlines = klineArray.map((k: any) => KlineDataSchema.parse(k));
    this.latestData.kline.set(interval, validatedKlines);
    
    this.emit('kline', {
      interval,
      data: this.transformKlineData(validatedKlines),
    });
  }

  private async processPriceFile(filePath: string): Promise<void> {
    const content = await fs.readFile(filePath, 'utf-8');
    const lines = content.trim().split('\n').filter(line => line.trim());

    if (lines.length === 0) return;

    const latestLine = lines[lines.length - 1];
    try {
      const legacyData = JSON.parse(latestLine);
      if (legacyData.mp) {
        // Convert legacy format to expected format
        const convertedData = {
          ch: 'price',
          symbol: 'ETHUSDT',
          ts: legacyData.timestamp || Date.now(),
          data: {
            ip: legacyData.ip || '0',
            mp: legacyData.mp,
            fr: legacyData.fr || '0',
            ft: legacyData.ft || '',
            nft: legacyData.nft || '',
          },
        };
        this.latestData.price = convertedData;
        this.emit('price', convertedData);
      }
    } catch (error) {
      console.error('Failed to parse price data:', error);
    }
  }

  private async processTradeFile(filePath: string): Promise<void> {
    const content = await fs.readFile(filePath, 'utf-8');
    const lines = content.trim().split('\n').filter(line => line.trim());

    if (lines.length === 0) return;

    // Process the last 10 lines to get recent trades
    const recentLines = lines.slice(-10);
    const allNewTrades: any[] = [];

    for (const line of recentLines) {
      try {
        const tradeData = JSON.parse(line);
        if (tradeData.trades && Array.isArray(tradeData.trades)) {
          const trades = tradeData.trades.map((trade: any) => ({
            id: `${trade.t}_${trade.p}_${trade.v}`, // Create unique ID
            price: parseFloat(trade.p),
            volume: parseFloat(trade.v),
            side: trade.s as 'buy' | 'sell',
            timestamp: tradeData.timestamp || Date.now(),
          }));
          allNewTrades.push(...trades);
        }
      } catch (error) {
        console.error('Failed to parse trade line:', error);
      }
    }

    if (allNewTrades.length > 0) {
      // Sort by timestamp and remove duplicates
      const uniqueTrades = allNewTrades.filter((trade, index, self) =>
        index === self.findIndex(t => t.id === trade.id)
      ).sort((a, b) => b.timestamp - a.timestamp);

      this.latestData.trades = [...uniqueTrades, ...this.latestData.trades].slice(0, 100);
      this.emit('trade', uniqueTrades);
    }
  }

  private transformDepthData(depthData: DepthData): OrderBookData {
    const transformLevels = (levels: [string, string][], isBid: boolean) => {
      let total = 0;
      return levels.map(([priceStr, sizeStr]) => {
        const price = parseFloat(priceStr);
        const size = parseFloat(sizeStr);
        total += size;
        return { price, size, total };
      }).sort((a, b) => isBid ? b.price - a.price : a.price - b.price);
    };

    return {
      bids: transformLevels(depthData.bids, true),
      asks: transformLevels(depthData.asks, false),
      timestamp: depthData.timestamp,
    };
  }

  private transformKlineData(klineData: KlineData[]): CandlestickData[] {
    return klineData.map(k => ({
      x: parseInt(k.time),
      o: parseFloat(k.open),
      h: parseFloat(k.high),
      l: parseFloat(k.low),
      c: parseFloat(k.close),
      volume: parseFloat(k.baseVol),
    })).sort((a, b) => a.x - b.x);
  }

  public getLatestDepth(): OrderBookData | null {
    return this.latestData.depth ? this.transformDepthData(this.latestData.depth) : null;
  }

  public getLatestKline(interval: TimeFrame): CandlestickData[] {
    const klineData = this.latestData.kline.get(interval);
    return klineData ? this.transformKlineData(klineData) : [];
  }

  public getLatestTrades(): Trade[] {
    return this.latestData.trades;
  }

  public getMarketStats(): MarketStats | null {
    if (!this.latestData.price || this.latestData.kline.size === 0) {
      return null;
    }

    const currentPrice = parseFloat(this.latestData.price.data.mp);
    const kline1m = this.latestData.kline.get('1m');

    if (!kline1m || kline1m.length === 0) {
      return {
        currentPrice,
        change24h: 0,
        changePercent24h: 0,
        volume24h: 0,
        high24h: currentPrice,
        low24h: currentPrice,
        spread: 0,
        lastUpdate: this.latestData.price.ts,
      };
    }

    const prices = kline1m.map(k => parseFloat(k.close));
    const volumes = kline1m.map(k => parseFloat(k.baseVol));
    const high24h = Math.max(...prices);
    const low24h = Math.min(...prices);
    const volume24h = volumes.reduce((sum, vol) => sum + vol, 0);
    
    const price24hAgo = prices[0] || currentPrice;
    const change24h = currentPrice - price24hAgo;
    const changePercent24h = (change24h / price24hAgo) * 100;

    const depth = this.getLatestDepth();
    const spread = depth && depth.asks.length > 0 && depth.bids.length > 0
      ? depth.asks[0].price - depth.bids[0].price
      : 0;

    return {
      currentPrice,
      change24h,
      changePercent24h,
      volume24h,
      high24h,
      low24h,
      spread,
      lastUpdate: this.latestData.price.ts,
    };
  }

  public destroy(): void {
    if (this.watcher) {
      this.watcher.close();
      this.watcher = null;
    }
    this.removeAllListeners();
  }
}
