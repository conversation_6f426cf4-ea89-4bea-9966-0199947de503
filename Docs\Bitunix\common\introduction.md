[Skip to content](https://openapidoc.bitunix.com/doc/common/introduction.html#VPContent)

Return to top

# API Introduction [​](https://openapidoc.bitunix.com/doc/common/introduction.html\#api-introduction)

Welcome to the Bitunix API Documentation 🎉 This OpenAPI documentation provides a comprehensive guide to integrating with our system. Here, you will find detailed information about available endpoints, request/response formats, authentication requirements, and usage examples to help you seamlessly connect with our services.

Whether you are a developer exploring integration possibilities or optimizing your existing workflow, this documentation is your one-stop reference for making the most out of our API.

### OpenAPI Demo [​](https://openapidoc.bitunix.com/doc/common/introduction.html\#openapi-demo)

You can fill in your API key and API secret to try running the official demo provided.

[Github Demo](https://github.com/BitunixOfficial/open-api)

### Preparing for Access [​](https://openapidoc.bitunix.com/doc/common/introduction.html\#preparing-for-access)

If you need to use the API, please [log in](https://www.bitunix.com/login) to complete the application of the API key and the configuration, and then develop and trade according to the details of this document.

You can click [here](http://www.bitunix.com/account/apiManagement) to create an API key.

Please make sure to remember the following information after you have successfully created the API Key:

- APIKey: Identity for API transactions, generated by random algorithm.
- SecretKey: private key, randomly generated by the system, used for signature generation.

> Risks: These two keys are closely related to the security of your account, please keep in mind that do not disclose them to others at any time. Any leakage of these two keys may cause loss of your assets. If you find any leakage of APIKey, please delete the APIKey as soon as possible.

### Interface Type [​](https://openapidoc.bitunix.com/doc/common/introduction.html\#interface-type)

- Public
- Private Interface

#### Public Interface [​](https://openapidoc.bitunix.com/doc/common/introduction.html\#public-interface)

> Public interface can be used to obtain configuration information and market data. Public requests can be invoked without authentication.

#### private interface [​](https://openapidoc.bitunix.com/doc/common/introduction.html\#private-interface)

> Private interface can be used for order management and account management. Each private request must be \[signed\] ( sign.md) using a canonical form of authentication. The private interface requires authentication using your APIKey.

### API domain name [​](https://openapidoc.bitunix.com/doc/common/introduction.html\#api-domain-name)

You can do this yourself using the Rest API access method.

| Domain Name | REST API |
| --- | --- |
| Primary Domain | [https://fapi.bitunix.com](https://fapi.bitunix.com/) |

### API Validation [​](https://openapidoc.bitunix.com/doc/common/introduction.html\#api-validation)

The header of all REST requests must contain the following keys:

- api-key: api-key of the request
- nonce: random string, 32-bit, generated by the caller
- timestamp: current timestamp, milliseconds
- sign: signature string (see [signature](https://openapidoc.bitunix.com/doc/common/sign.html) description)
- Content-Type: Uniformly set to application/json.

### Interaction Request [​](https://openapidoc.bitunix.com/doc/common/introduction.html\#interaction-request)

All requests are based on the Https protocol, the Content-Type in the POST request header should be set to: ' application/json'.

#### Description of Interaction Request [​](https://openapidoc.bitunix.com/doc/common/introduction.html\#description-of-interaction-request)

- Request parameter: encapsulate the parameter according to the request parameter of the interface.
- Submit request parameters: submit the encapsulated request parameters to the server via GET/POST.
- Server Response: The server first performs parameter security verification on the user request data and returns the response data in JSON format to the user according to the business logic after passing the verification.
- Data Processing: Process the response data from the server.

#### Success [​](https://openapidoc.bitunix.com/doc/common/introduction.html\#success)

HTTP status code 200 indicates a successful response and may contain content. If the response contains content, it will be displayed in the corresponding return content.

#### Common Error Codes [​](https://openapidoc.bitunix.com/doc/common/introduction.html\#common-error-codes)

- 400 Bad Request - Invalid request format
- 403 Forbidden - You do not have access to the requested resource.
- 404 Not Found No request found
- 500 Internal Server Error - We had a problem with our server internal error; if failed body with error description message
- See [Error Code](https://openapidoc.bitunix.com/doc/ErrorCode/error_code.html) for more details.

### standardized specification [​](https://openapidoc.bitunix.com/doc/common/introduction.html\#standardized-specification)

#### Timestamp [​](https://openapidoc.bitunix.com/doc/common/introduction.html\#timestamp)

The timestamp in the request signature is in milliseconds and is standardized to UTC time.

#### Request Formats [​](https://openapidoc.bitunix.com/doc/common/introduction.html\#request-formats)

Currently, there are only two request formats: GET and POST.

- GET: Parameters are transferred to the server via queryString in the path.
- POST: Parameters are transferred to the server by sending body in json format.

* * *