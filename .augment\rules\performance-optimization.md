---
type: "agent_requested"
description: "Performance optimization and monitoring rules"
---
# Performance Optimization Rules

### Description

Rules for optimizing performance, monitoring resource usage, and maintaining efficiency in the Bitunix Trading Application.

### Performance Targets

| Component | Metric | Target | Action if Exceeded |
| --- | --- | --- | --- |
| API Client | Requests/minute | <75 (12.5% of limit) | Reduce frequency |
| WebSocket Server | Latency | <100ms | Optimize processing |
| Dashboard | Frame rate | 60fps | Reduce data points |
| Data Storage | Write speed | >1MB/sec | Batch operations |
| Memory Usage | Per service | <100MB | Implement cleanup |

### Resource Monitoring Rules

| Resource | Limit | Check Frequency | Alert Threshold |
| --- | --- | --- | --- |
| Memory | 100MB per service | Every 30 seconds | 80MB |
| CPU | 50% per core | Every 10 seconds | 40% |
| Disk I/O | 10MB/sec | Every minute | 8MB/sec |
| Network | 1MB/sec | Every minute | 800KB/sec |
| File handles | 100 open files | Every minute | 80 files |

### Optimization Strategies

| Bottleneck | Solution | Implementation |
| --- | --- | --- |
| High memory | Data cleanup | Periodic garbage collection |
| Slow rendering | Data throttling | Limit update frequency |
| API rate limits | Request batching | Combine multiple requests |
| Large files | Compression | Gzip storage files |
| Slow queries | Indexing | Index frequently accessed data |

### Data Processing Optimization

| Process | Current | Target | Method |
| --- | --- | --- | --- |
| Order book updates | 1 second | 500ms | Optimize parsing |
| Chart rendering | 100ms | 50ms | Canvas optimization |
| File writing | 10MB/sec | 20MB/sec | Async I/O |
| WebSocket messages | 50ms | 25ms | Message batching |

### Memory Management Rules

| Data Type | Retention | Cleanup Trigger | Method |
| --- | --- | --- | --- |
| Chart data | 200 points | Point limit reached | Remove oldest |
| Trade history | 100 trades | Trade limit reached | FIFO removal |
| Order book | Current snapshot | New snapshot | Replace previous |
| Log entries | 1000 entries | Entry limit reached | Rotate logs |

### Caching Rules

| Cache Type | TTL | Size Limit | Eviction |
| --- | --- | --- | --- |
| API responses | 5 seconds | 10MB | LRU |
| Processed data | 30 seconds | 50MB | LRU |
| Chart data | 1 minute | 20MB | Time-based |
| Configuration | Session | 1MB | Manual clear |

### Performance Monitoring

| Metric | Collection | Storage | Alerting |
| --- | --- | --- | --- |
| Response times | Every request | 24 hours | >500ms |
| Error rates | Every error | 7 days | >1% |
| Resource usage | Every 30s | 24 hours | >80% |
| Throughput | Every minute | 24 hours | <target |
