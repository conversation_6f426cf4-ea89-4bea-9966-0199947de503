# Documentation Template Usage Guide

## Quick Reference

### Template Selection
- **[Component Template](./component-template.md)** - React/UI components, visualization components
- **[Service Template](./service-template.md)** - Backend services, API clients, data processors
- **[Configuration Template](./configuration-template.md)** - Environment setup, configuration management

### Quality Standards (Bitunix Reference)
- **Length**: 45-75 lines (matching Bitunix 47-71 line range)
- **Structure**: Description → Parameters → Example → Output
- **Cross-references**: Minimal overhead, maximum value
- **Technical focus**: Specification over explanation

## Template Application Process

### 1. Select Template
Choose based on documentation type:
```
Component Documentation → component-template.md
Service Documentation → service-template.md  
Configuration Documentation → configuration-template.md
```

### 2. Replace Placeholders
```markdown
# Required Replacements:
[Component/Service/Configuration Name] → Actual name
[src] paths → Actual file paths
Parameter tables → Actual parameters/props
Usage examples → Practical examples
Cross-references → Valid links
```

### 3. Verify Standards
```markdown
# Quality Checklist:
[ ] Length: 45-75 lines
[ ] Structure: Follows Bitunix pattern
[ ] Cross-references: Added where valuable
[ ] Tables: Consistent format
[ ] Examples: Single, practical, copy-paste ready
[ ] Technical accuracy: All details verified
```

## Cross-Reference Standards

### Reference Types
```markdown
[src]     → Source code files (../../path/to/file.ts)
[→]       → Type/interface documentation (../data-formats/market-data.md#type)
[api]     → Bitunix API documentation (../../Bitunix/endpoint.md)
[ws]      → WebSocket related services (./websocket-client.md)
[chart]   → Chart/visualization components (./price-chart.md)
[config]  → Configuration files (../configuration.md)
```

### Cross-Reference Guidelines
- **Add value**: Only include references that provide navigation benefit
- **Keep minimal**: 15-25 characters overhead per reference
- **Be specific**: Link to exact sections/functions when possible
- **Maintain accuracy**: Verify all links point to existing content

## Section Standards

### Description Section
```markdown
### Description [src](../../path/to/implementation.ts)

[Single sentence describing purpose and primary function.]
```
- **Length**: 1-2 sentences maximum
- **Focus**: Purpose and primary function
- **Style**: Technical, concise, no marketing language

### Parameters/Props Section
```markdown
### Props [src](../../path/to/component.tsx#PropsInterface)

| Parameter | Type | Required | Description |
| --- | --- | --- | --- |
| paramName | TypeName [→](../data-formats/market-data.md#typename) | Yes | Brief description |
```
- **Format**: Consistent table structure
- **Types**: Include cross-references for complex types
- **Descriptions**: Brief, technical, include examples where helpful
- **Required**: Clearly mark required vs optional

### Usage Example Section
```markdown
Usage Example:

```typescript
// Single, practical, copy-paste ready example
const result = useComponent({
  requiredParam: 'value',
  optionalParam: true,
});
```
```
- **Quantity**: Single example only
- **Quality**: Immediately usable, copy-paste ready
- **Focus**: Most common use case
- **Completeness**: Include essential parameters

### Output/Response Section
```markdown
### Data Output [src](../../path/to/interface.ts#OutputInterface)

| Field | Type | Description |
| --- | --- | --- |
| outputField | number | Description of output |
| complexField | ComplexType [→](../data-formats/market-data.md#complextype) | Complex type reference |
```
- **Format**: Table structure for structured data
- **Types**: Include cross-references for complex types
- **Completeness**: Essential fields only
- **Accuracy**: Match actual implementation

## Common Patterns

### React Component Documentation
```markdown
# ComponentName

### Description [src](../../dashboard/src/components/ComponentName.tsx)
### Props [src](../../dashboard/src/components/ComponentName.tsx#ComponentNameProps)
### Data Output [src](../../dashboard/src/components/ComponentName.tsx#ComponentNameData)
```

### Service Documentation
```markdown
# ServiceName

Rate Limit: X req/sec/ip [api](../../Bitunix/common/introduction.md#rate-limits)

### Description [src](../../src/services/ServiceName.ts)
### Configuration [src](../../src/config/config.ts#ServiceConfig)
### Events [src](../../src/services/ServiceName.ts#events)
```

### Configuration Documentation
```markdown
# ConfigurationName

### Description [src](../../src/config/configFile.ts)
### Configuration Parameters [src](../../src/config/configFile.ts#ConfigInterface)
### Environment Variables
```

## Validation Checklist

### Before Publishing
- [ ] **Length Check**: 45-75 lines total
- [ ] **Link Validation**: All cross-references point to existing content
- [ ] **Technical Accuracy**: All parameters, types, and examples verified
- [ ] **Consistency**: Follows established patterns and formatting
- [ ] **Completeness**: All essential information included
- [ ] **Conciseness**: No unnecessary verbosity or redundancy

### Quality Metrics
- **Information Density**: 8-10 technical facts per 10 lines
- **Navigation Value**: Cross-references provide clear navigation paths
- **Usability**: Examples are immediately usable
- **Maintainability**: Clear relationship to implementation files

## AI-Specific Usage Rules

### Context Management
- Prioritize essential sections when context limits reached
- Preserve structure and cross-references over verbose examples
- Use decision trees for template selection efficiency

### Conflict Resolution
- Code implementation > Documentation > User preference
- Most recent template > Legacy patterns
- Template standards > Individual customization

### Quality Validation
- Automated checks: length, structure, cross-references
- Manual validation: technical accuracy, example quality
- Performance metrics: creation speed, compliance rate

For comprehensive AI optimization rules: [Rules Directory](../rules/)

## Template Maintenance

### When to Update Templates
- New cross-reference patterns emerge
- Quality standards evolve
- Bitunix documentation patterns change
- AI usage feedback indicates improvements needed

### Template Evolution
- Monitor AI usage patterns and efficiency
- Collect feedback on template effectiveness for AI systems
- Update based on documentation quality metrics and AI parsing optimization
- Maintain alignment with Bitunix reference standard and AI-specific needs
