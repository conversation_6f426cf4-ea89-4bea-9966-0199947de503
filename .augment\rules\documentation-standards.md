---
type: "agent_requested"
description: "Documentation creation and maintenance standards"
---
# Documentation Standards

### Description

Rules for creating and maintaining documentation following Bitunix reference standards.

### Documentation Requirements [templates](../templates/)

| Standard | Requirement | Check |
| --- | --- | --- |
| Length | 45-75 lines per file | Count lines |
| Structure | Description → Parameters → Example → Output | Verify sections |
| Cross-references | Minimum 3 per file | Count [src], [→], [api] links |
| Table format | Parameter \| Type \| Required \| Description | Check columns |
| Examples | Single working example | Test code |

### Context Priority Rules

| Scenario | Load Order | Action |
| --- | --- | --- |
| Context limit | Current task → Dependencies → Examples | Keep essential info |
| Multiple files | Primary → Related → References | Load by importance |
| Conflicts | Code → Recent docs → API | Use most reliable source |

### File Creation Rules

| File Type | Template | Required Sections |
| --- | --- | --- |
| Component | component-template.md | Description, Props, Usage, Output |
| Service | service-template.md | Description, Configuration, Usage, Events |
| Config | configuration-template.md | Description, Parameters, Example, Validation |

### Cross-Reference Rules

| Link Type | Format | Target |
| --- | --- | --- |
| Source code | [src](path/file.ts) | Implementation files |
| Types | [→](../data-formats/type.md) | Type definitions |
| API docs | [api](../../Bitunix/endpoint.md) | Bitunix API |
| Services | [ws](./service.md) | Related services |

### Validation Rules

| Check | Method | Fix |
| --- | --- | --- |
| Missing file | Check path exists | Create or fix path |
| Wrong format | Compare to template | Reformat using template |
| Broken links | Test all references | Update or remove links |
| Too long | Count lines | Remove non-essential content |
| Missing sections | Check template | Add required sections |

### Update Rules

| Change Type | Update Target | Action |
| --- | --- | --- |
| API change | Application docs using API | Update affected docs |
| Code change | Component docs | Update examples and types |
| Config change | Installation docs | Update setup instructions |
| Template change | All docs using template | Apply new template |
