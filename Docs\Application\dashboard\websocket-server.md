# WebSocket Server

### Description [src](../../dashboard/server.js)

Real-time data distribution bridge between file-based storage and dashboard clients.

### Configuration [src](../../dashboard/server.js#config)

| Parameter | Type | Required | Description |
| --- | --- | --- | --- |
| WS_PORT | number | No | WebSocket server port (default: 8080) |
| DATA_DIR | string | No | Data directory path (default: ../data) |
| perMessageDeflate | boolean | No | Message compression (default: false) |

Usage Example:

```bash
# Start WebSocket server
cd dashboard
WS_PORT=8080 DATA_DIR=../data node server.js

# Or using npm script
npm run websocket:dev
```

### Message Types [src](../../dashboard/src/lib/dataProcessor.ts)

| Type | Data | Description |
| --- | --- | --- |
| depth | OrderBookData [→](../data-formats/market-data.md#orderbookdata) | Order book updates |
| kline | CandlestickData[] [→](../data-formats/market-data.md#candlestickdata) | Kline/candlestick data |
| trade | Trade[] [→](../data-formats/market-data.md#trade) | Trade executions |
| price | MarketPriceData | Real-time price updates |
| stats | MarketStats [→](../data-formats/market-data.md#marketstats) | Market statistics |

### Data Flow

```
Data Files → File Watcher → Data Processor → WebSocket Server → Dashboard
     ↓            ↓             ↓               ↓                    ↓
JSON Lines   Change Events   Processed Data   Real-time Updates   Live UI
```

### Events [src](../../dashboard/src/lib/dataProcessor.ts#events)

| Event | Parameters | Description |
| --- | --- | --- |
| depth | OrderBookData | Order book updates |
| kline | CandlestickData[] | Kline/candlestick data |
| trade | Trade[] | Trade executions |
| price | MarketPriceData | Real-time price updates |
| stats | MarketStats | Market statistics |
| error | Error | Processing errors |

### Performance Optimization [src](../../dashboard/server.js#optimization)

| Feature | Value | Description |
| --- | --- | --- |
| Message Throttling | 100ms | Prevents high-frequency spam |
| Connection Heartbeat | 30s | Detects dead connections |
| Trade History Limit | 100 | Memory management |
| Kline Points Limit | 200 | Memory management |
