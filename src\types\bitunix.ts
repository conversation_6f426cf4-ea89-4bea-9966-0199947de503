import { z } from 'zod';

export const BitunixApiResponseSchema = z.object({
  code: z.number(),
  data: z.unknown(),
  msg: z.string(),
});

export const DepthDataSchema = z.object({
  asks: z.array(z.tuple([z.string(), z.string()])),
  bids: z.array(z.tuple([z.string(), z.string()])),
});

export const DepthResponseSchema = BitunixApiResponseSchema.extend({
  data: DepthDataSchema,
});

export const KlineDataSchema = z.object({
  open: z.string(),
  high: z.string(),
  low: z.string(),
  close: z.string(),
  time: z.string(),
  quoteVol: z.string(),
  baseVol: z.string(),
  type: z.string().optional(),
});

export const KlineResponseSchema = BitunixApiResponseSchema.extend({
  data: z.array(KlineDataSchema),
});

export const MarketPriceDataSchema = z.object({
  ip: z.string(),
  mp: z.string(),
  fr: z.string(),
  ft: z.string(),
  nft: z.string(),
});

export const MarketPriceMessageSchema = z.object({
  ch: z.literal('price'),
  symbol: z.string(),
  ts: z.number(),
  data: MarketPriceDataSchema,
});

export const TradeDataSchema = z.object({
  t: z.string(),
  p: z.string(),
  v: z.string(),
  s: z.enum(['buy', 'sell']),
});

export const TradeMessageSchema = z.object({
  ch: z.literal('trade'),
  symbol: z.string(),
  ts: z.number(),
  data: z.array(TradeDataSchema),
});

export const WebSocketPingSchema = z.object({
  op: z.literal('ping'),
  ping: z.number(),
});

export const WebSocketPongSchema = z.object({
  op: z.literal('ping'),
  pong: z.number(),
  ping: z.number(),
});

export const WebSocketSubscribeSchema = z.object({
  op: z.literal('subscribe'),
  args: z.array(z.object({
    symbol: z.string(),
    ch: z.string(),
  })),
});

export type BitunixApiResponse = z.infer<typeof BitunixApiResponseSchema>;
export type DepthData = z.infer<typeof DepthDataSchema>;
export type DepthResponse = z.infer<typeof DepthResponseSchema>;
export type KlineData = z.infer<typeof KlineDataSchema>;
export type KlineResponse = z.infer<typeof KlineResponseSchema>;
export type MarketPriceData = z.infer<typeof MarketPriceDataSchema>;
export type MarketPriceMessage = z.infer<typeof MarketPriceMessageSchema>;
export type TradeData = z.infer<typeof TradeDataSchema>;
export type TradeMessage = z.infer<typeof TradeMessageSchema>;
export type WebSocketPing = z.infer<typeof WebSocketPingSchema>;
export type WebSocketPong = z.infer<typeof WebSocketPongSchema>;
export type WebSocketSubscribe = z.infer<typeof WebSocketSubscribeSchema>;

export interface BitunixConfig {
  apiKey: string;
  secretKey: string;
  baseUrl: string;
  wsUrl: string;
}

export interface RateLimitInfo {
  requestsRemaining: number;
  resetTime: number;
}

export type KlineInterval = '1m' | '3m' | '5m' | '15m' | '30m' | '1h' | '2h' | '4h' | '6h' | '8h' | '12h' | '1d' | '3d' | '1w' | '1M';
export type KlineType = 'LAST_PRICE' | 'MARK_PRICE';
export type DepthLimit = '1' | '5' | '15' | '50' | 'max';
