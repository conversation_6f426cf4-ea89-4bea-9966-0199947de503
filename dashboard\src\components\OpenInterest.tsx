'use client'

import React, { useState, useEffect } from 'react'
import { OrderBookData } from '@/types/market'

interface OpenInterestProps {
  orderBook: OrderBookData | null
}

interface OpenInterestData {
  totalLiquidity: number
  bidLiquidity: number
  askLiquidity: number
  liquidityRatio: number
  averageSpread: number
  marketDepth: {
    bid: number
    ask: number
  }
}

export default function OpenInterest({ orderBook }: OpenInterestProps) {
  const [oiData, setOiData] = useState<OpenInterestData>({
    totalLiquidity: 0,
    bidLiquidity: 0,
    askLiquidity: 0,
    liquidityRatio: 0,
    averageSpread: 0,
    marketDepth: { bid: 0, ask: 0 },
  })

  useEffect(() => {
    if (!orderBook || !orderBook.bids || !orderBook.asks ||
        !Array.isArray(orderBook.bids) || !Array.isArray(orderBook.asks) ||
        orderBook.bids.length === 0 || orderBook.asks.length === 0) {
      return
    }

    const bidLiquidity = orderBook.bids.reduce((total, level) => {
      return total + (level.price * level.size)
    }, 0)

    const askLiquidity = orderBook.asks.reduce((total, level) => {
      return total + (level.price * level.size)
    }, 0)

    const totalLiquidity = bidLiquidity + askLiquidity
    const liquidityRatio = bidLiquidity / (askLiquidity || 1)
    const topLevels = Math.min(5, orderBook.bids.length, orderBook.asks.length)

    let totalSpread = 0

    for (let i = 0; i < topLevels; i++) {
      const bidPrice = orderBook.bids[i].price
      const askPrice = orderBook.asks[i].price
      totalSpread += (askPrice - bidPrice)
    }

    const averageSpread = totalSpread / topLevels

    const bidDepth = orderBook.bids.reduce((total, level) => {
      return total + level.size
    }, 0)

    const askDepth = orderBook.asks.reduce((total, level) => {
      return total + level.size
    }, 0)

    setOiData({
      totalLiquidity,
      bidLiquidity,
      askLiquidity,
      liquidityRatio,
      averageSpread,
      marketDepth: {
        bid: bidDepth,
        ask: askDepth,
      },
    })
  }, [orderBook])

  const formatCurrency = (value: number): string => {
    if (value >= 1000000) {
      return `$${(value / 1000000).toFixed(2)}M`
    } else if (value >= 1000) {
      return `$${(value / 1000).toFixed(2)}K`
    }
    return `$${value.toFixed(2)}`
  }

  const formatVolume = (volume: number): string => {
    if (volume >= 1000000) {
      return `${(volume / 1000000).toFixed(2)}M`
    } else if (volume >= 1000) {
      return `${(volume / 1000).toFixed(2)}K`
    }
    return volume.toFixed(2)
  }

  const getLiquidityRatioColor = (): string => {
    if (oiData.liquidityRatio > 1.2) return 'text-success-600' // More bids
    if (oiData.liquidityRatio < 0.8) return 'text-danger-600' // More asks
    return 'text-gray-600' // Balanced
  }

  const getLiquidityRatioText = (): string => {
    if (oiData.liquidityRatio > 1.2) return 'Bid Heavy'
    if (oiData.liquidityRatio < 0.8) return 'Ask Heavy'
    return 'Balanced'
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">
          Market Liquidity & Depth
        </h3>
        <div className="text-xs text-gray-500">
          {orderBook ? `${orderBook.bids.length + orderBook.asks.length} levels` : 'No data'}
        </div>
      </div>

      {/* Total Liquidity */}
      <div className="mb-4">
        <div className="text-2xl font-bold text-gray-900">
          {formatCurrency(oiData.totalLiquidity)}
        </div>
        <div className="text-sm text-gray-500">Total Liquidity</div>
      </div>

      {/* Liquidity Breakdown */}
      <div className="grid grid-cols-2 gap-4 mb-4">
        <div className="text-center">
          <div className="text-xs text-gray-500 mb-1">Bid Liquidity</div>
          <div className="text-sm font-semibold text-success-600">
            {formatCurrency(oiData.bidLiquidity)}
          </div>
        </div>
        <div className="text-center">
          <div className="text-xs text-gray-500 mb-1">Ask Liquidity</div>
          <div className="text-sm font-semibold text-danger-600">
            {formatCurrency(oiData.askLiquidity)}
          </div>
        </div>
      </div>

      {/* Market Depth */}
      <div className="grid grid-cols-2 gap-4 mb-4">
        <div className="text-center">
          <div className="text-xs text-gray-500 mb-1">Bid Depth</div>
          <div className="text-sm font-semibold text-success-600">
            {formatVolume(oiData.marketDepth.bid)}
          </div>
        </div>
        <div className="text-center">
          <div className="text-xs text-gray-500 mb-1">Ask Depth</div>
          <div className="text-sm font-semibold text-danger-600">
            {formatVolume(oiData.marketDepth.ask)}
          </div>
        </div>
      </div>

      {/* Liquidity Ratio & Spread */}
      <div className="border-t border-gray-200 pt-3 space-y-2">
        <div className="flex justify-between items-center">
          <span className="text-xs text-gray-500">Liquidity Balance:</span>
          <span className={`text-sm font-semibold ${getLiquidityRatioColor()}`}>
            {getLiquidityRatioText()}
          </span>
        </div>
        
        <div className="flex justify-between items-center">
          <span className="text-xs text-gray-500">Avg Spread:</span>
          <span className="text-sm font-semibold text-gray-600">
            ${oiData.averageSpread.toFixed(2)}
          </span>
        </div>

        <div className="flex justify-between items-center">
          <span className="text-xs text-gray-500">Ratio:</span>
          <span className="text-sm font-semibold text-gray-600">
            {oiData.liquidityRatio.toFixed(2)}:1
          </span>
        </div>
      </div>

      {/* Explanation */}
      <div className="mt-3 text-xs text-gray-400">
        Liquidity = Total USD value in order book. Higher values indicate better market depth.
      </div>
    </div>
  )
}
