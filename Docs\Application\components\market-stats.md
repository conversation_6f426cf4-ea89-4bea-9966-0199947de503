# Market Stats Component

### Description [src](../../dashboard/src/components/MarketStats.tsx)

Market statistics panel displaying key metrics including price, volume, and market sentiment indicators.

### Props [src](../../dashboard/src/components/MarketStats.tsx#MarketStatsProps)

| Parameter | Type | Required | Description |
| --- | --- | --- | --- |
| stats | MarketStats [→](../data-formats/market-data.md#marketstats) | Yes | Market statistics data |
| showChange | boolean | No | Show 24h price change (default: true) |
| showVolume | boolean | No | Show 24h volume (default: true) |
| showHighLow | boolean | No | Show 24h high/low (default: true) |

Usage Example:

```typescript
<MarketStats
  stats={marketStatsData}
  showChange={true}
  showVolume={true}
  showHighLow={true}
/>
```

### Display Layout [src](../../dashboard/src/components/MarketStats.tsx#layout)

```
ETHUSDT Market Statistics
┌─────────────────┬─────────────────┬─────────────────┐
│ Current Price   │ 24h Change      │ 24h Volume      │
│ $2,984.50      │ +$45.25 (+1.54%)│ 1,234.567 ETH  │
├─────────────────┼─────────────────┼─────────────────┤
│ 24h High        │ 24h Low         │ Spread          │
│ $2,995.00      │ $2,970.25       │ $0.25 (0.008%) │
└─────────────────┴─────────────────┴─────────────────┘
```

### Real-Time Updates [src](../../dashboard/src/components/MarketStats.tsx#updates)

| Metric | Update Frequency | Source |
| --- | --- | --- |
| Current Price | Real-time | WebSocket price updates [→](../dashboard/websocket-server.md) |
| 24h Change | 1 minute | Calculated from historical data |
| 24h Volume | 5 minutes | Aggregated from trade data |
| High/Low | 1 minute | Rolling 24-hour calculation |
| Spread | 1 second | Order book best bid/ask |

### Color Coding [src](../../dashboard/src/components/MarketStats.tsx#colors)

| Metric | Positive | Negative | Neutral |
| --- | --- | --- | --- |
| Price Change | Green | Red | Gray |
| Percentage Change | Green | Red | Gray |
| Volume | Blue | Blue | Blue |
| Spread | Orange | Orange | Orange |

### Calculation Methods [src](../../dashboard/src/components/MarketStats.tsx#calculations)

| Metric | Formula | Description |
| --- | --- | --- |
| 24h Change | Current Price - Price 24h ago | Absolute price change |
| 24h Change % | (24h Change / Price 24h ago) × 100 | Percentage price change |
| 24h Volume | Σ(Trade Volume) last 24h | Total trading volume |
| 24h High | Max(Price) last 24h | Highest price in 24h |
| 24h Low | Min(Price) last 24h | Lowest price in 24h |

### Responsive Design [src](../../dashboard/src/components/MarketStats.tsx#responsive)

| Screen Size | Layout | Columns |
| --- | --- | --- |
| Mobile (<768px) | Stacked | 1 column |
| Tablet (768-1024px) | Grid | 2 columns |
| Desktop (>1024px) | Grid | 3 columns |

### Data Output [src](../../dashboard/src/components/MarketStats.tsx#output)

| Field | Type | Description |
| --- | --- | --- |
| currentPrice | number | Latest market price |
| change24h | number | 24-hour price change |
| changePercent24h | number | 24-hour percentage change |
| volume24h | number | 24-hour trading volume |
| high24h | number | 24-hour high price |
| low24h | number | 24-hour low price |
| spread | number | Current bid-ask spread |
| lastUpdate | number | Last update timestamp |
