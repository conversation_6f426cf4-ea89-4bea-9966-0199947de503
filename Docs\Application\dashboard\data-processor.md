# Data Processor

### Description [src](../../dashboard/src/lib/dataProcessor.ts)

File watching and data transformation service that monitors storage directory and processes market data for dashboard consumption.

### Configuration [src](../../dashboard/src/lib/dataProcessor.ts#config)

| Parameter | Type | Required | Description |
| --- | --- | --- | --- |
| dataDir | string | Yes | Data directory path (default: ../data) |
| watchOptions | object | No | Chokidar file watcher options |
| processingInterval | number | No | Data processing interval (default: 1000ms) |
| maxFileSize | number | No | Max file size to process (default: 10MB) |

Usage Example:

```typescript
const dataProcessor = new DataProcessor('../data', {
  watchOptions: {
    ignored: /(^|[\/\\])\../,
    persistent: true,
    ignoreInitial: false,
  },
  processingInterval: 1000,
  maxFileSize: 10 * 1024 * 1024,
});

// Start watching
dataProcessor.start();

// Handle events
dataProcessor.on('depth', (data) => {
  console.log('Order book update:', data);
});

dataProcessor.on('kline', (data) => {
  console.log('Kline update:', data);
});
```

### File Processing [src](../../dashboard/src/lib/dataProcessor.ts#processing)

| File Type | Directory | Processing Method |
| --- | --- | --- |
| Order Book | depth/ | processDepthFile() |
| Candlestick | kline/ | processKlineFile() |
| Trades | trades/ | processTradeFile() |
| Market Price | market-price/ | processPriceFile() |

### Events [src](../../dashboard/src/lib/dataProcessor.ts#events)

| Event | Data Type | Description |
| --- | --- | --- |
| depth | OrderBookData [→](../data-formats/market-data.md#orderbookdata) | Order book updates |
| kline | CandlestickData[] [→](../data-formats/market-data.md#candlestickdata) | Kline data updates |
| trade | Trade[] [→](../data-formats/market-data.md#trade) | Trade execution updates |
| price | MarketPriceData | Real-time price updates |
| stats | MarketStats [→](../data-formats/market-data.md#marketstats) | Calculated market statistics |
| error | Error | Processing errors |

### Data Transformation [src](../../dashboard/src/lib/dataProcessor.ts#transformation)

| Input Format | Output Format | Description |
| --- | --- | --- |
| JSON Lines | OrderBookData | Transform depth data to chart format |
| JSON Lines | CandlestickData[] | Transform kline data for Chart.js |
| JSON Lines | Trade[] | Transform trade data for feed display |
| JSON Lines | MarketStats | Calculate aggregated statistics |

### File Watching [src](../../dashboard/src/lib/dataProcessor.ts#watcher)

| Feature | Description |
| --- | --- |
| Real-time Monitoring | Chokidar-based file system watching |
| Change Detection | File add, change, and delete events |
| Error Recovery | Automatic watcher restart on errors |
| Performance Optimization | Debounced file processing |

### Memory Management [src](../../dashboard/src/lib/dataProcessor.ts#memory)

| Feature | Value | Description |
| --- | --- | --- |
| Data Buffer Limit | 1000 records | Maximum records in memory |
| File Size Limit | 10MB | Maximum file size to process |
| Processing Queue | 100 files | Maximum files in processing queue |
| Cleanup Interval | 60 seconds | Memory cleanup frequency |
