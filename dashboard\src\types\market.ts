import { z } from 'zod';

// Market data types based on the collector's data structure
export const DepthDataSchema = z.object({
  asks: z.array(z.tuple([z.string(), z.string()])),
  bids: z.array(z.tuple([z.string(), z.string()])),
  symbol: z.string(),
  timestamp: z.number(),
  limit: z.string(),
});

export const KlineDataSchema = z.object({
  open: z.string(),
  high: z.string(),
  low: z.string(),
  close: z.string(),
  time: z.string(),
  quoteVol: z.string(),
  baseVol: z.string(),
  symbol: z.string(),
  interval: z.string(),
  timestamp: z.number(),
});

// Market price data schema based on official Bitunix WebSocket API
export const MarketPriceDataSchema = z.object({
  ch: z.string(),
  symbol: z.string(),
  ts: z.number(),
  data: z.object({
    ip: z.string(), // Index price
    mp: z.string(), // Market price
    fr: z.string(), // Funding rate
    ft: z.string(), // Funding rate settlement time
    nft: z.string(), // Next funding rate settlement time
  }),
});

// Trade data schema based on official Bitunix WebSocket API
export const TradeDataSchema = z.object({
  ch: z.string(),
  symbol: z.string(),
  ts: z.number(),
  data: z.array(z.object({
    t: z.string(), // Timestamp
    p: z.string(), // Price
    v: z.string(), // Volume
    s: z.enum(['buy', 'sell']), // Side
  })),
});

export type DepthData = z.infer<typeof DepthDataSchema>;
export type KlineData = z.infer<typeof KlineDataSchema>;
export type MarketPriceData = z.infer<typeof MarketPriceDataSchema>;
export type TradeData = z.infer<typeof TradeDataSchema>;

export interface CandlestickData {
  x: number;
  o: number;
  h: number;
  l: number;
  c: number;
  volume: number;
}

export interface OrderBookLevel {
  price: number;
  size: number;
  total: number;
}

export interface OrderBookData {
  bids: OrderBookLevel[];
  asks: OrderBookLevel[];
  timestamp: number;
}

export interface Trade {
  id: string;
  price: number;
  volume: number;
  side: 'buy' | 'sell';
  timestamp: number;
}

export interface MarketStats {
  currentPrice: number;
  change24h: number;
  changePercent24h: number;
  volume24h: number;
  high24h: number;
  low24h: number;
  spread: number;
  lastUpdate: number;
}

export interface WebSocketMessage {
  type: 'depth' | 'kline' | 'price' | 'trade' | 'stats' | 'ping' | 'pong' | 'refresh' | 'error';
  data: any;
  timestamp: number;
}

export type TimeFrame = '1m' | '3m' | '5m' | '15m';

// Extended period options for Long/Short Ratio and future technical indicators
export type ExtendedTimeFrame = TimeFrame | '1h' | '4h' | '1d';

export interface DashboardState {
  isConnected: boolean;
  lastUpdate: number;
  selectedTimeframe: TimeFrame;
  autoRefresh: boolean;
  error: string | null;
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  timestamp: number;
}
