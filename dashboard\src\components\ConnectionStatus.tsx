'use client';

import React, { useState, useEffect } from 'react';
import { format } from 'date-fns';
import { Wifi, WifiOff, AlertCircle, Clock } from 'lucide-react';

interface ConnectionStatusProps {
  isConnected: boolean;
  lastUpdate: number;
  error: string | null;
}

export default function ConnectionStatus({
  isConnected,
  lastUpdate,
  error
}: ConnectionStatusProps) {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  const getStatusColor = () => {
    if (error) return 'status-disconnected';
    if (isConnected) return 'status-connected';
    return 'status-connecting';
  };

  const getStatusIcon = () => {
    if (error) return <AlertCircle className="h-4 w-4" />;
    if (isConnected) return <Wifi className="h-4 w-4" />;
    return <WifiOff className="h-4 w-4" />;
  };

  const getStatusText = () => {
    if (error) return 'Error';
    if (isConnected) return 'Connected';
    return 'Connecting...';
  };

  const timeSinceUpdate = Date.now() - lastUpdate;
  const isStale = timeSinceUpdate > 60000; // 1 minute

  return (
    <div className="flex items-center space-x-4">
      {/* Connection Status */}
      <div className={`status-indicator ${getStatusColor()}`}>
        {getStatusIcon()}
        <span className="ml-1">{getStatusText()}</span>
      </div>

      {/* Last Update */}
      {isConnected && (
        <div className={`flex items-center text-sm ${
          isStale ? 'text-warning-600' : 'text-gray-500'
        }`}>
          <Clock className="h-4 w-4 mr-1" />
          <span>
            {isClient ? format(new Date(lastUpdate), 'HH:mm:ss') : '--:--:--'}
          </span>
          {isStale && isClient && (
            <span className="ml-1 text-warning-600">(stale)</span>
          )}
        </div>
      )}

      {/* Error Message */}
      {error && (
        <div className="text-sm text-danger-600 max-w-xs truncate" title={error}>
          {error}
        </div>
      )}
    </div>
  );
}
