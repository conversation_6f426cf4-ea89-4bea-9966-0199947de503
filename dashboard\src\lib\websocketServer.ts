import { WebSocketServer, WebSocket } from 'ws';
import { DataProcessor } from './dataProcessor';
import { WebSocketMessage } from '@/types/market';

export class DashboardWebSocketServer {
  private wss: WebSocketServer;
  private dataProcessor: DataProcessor;
  private clients: Set<WebSocket> = new Set();
  private heartbeatInterval: NodeJS.Timeout | null = null;

  constructor(port: number = 8080, dataDir?: string) {
    this.wss = new WebSocketServer({ port });
    this.dataProcessor = new DataProcessor(dataDir);
    this.setupWebSocketServer();
    this.setupDataProcessor();
    this.startHeartbeat();
    
    console.log(`Dashboard WebSocket server running on port ${port}`);
  }

  private setupWebSocketServer(): void {
    this.wss.on('connection', (ws: WebSocket) => {
      console.log('Client connected');
      this.clients.add(ws);

      this.sendInitialData(ws);

      ws.on('message', (message: Buffer) => {
        try {
          const data = JSON.parse(message.toString());
          this.handleClientMessage(ws, data);
        } catch (error) {
          console.error('Error parsing client message:', error);
        }
      });

      ws.on('close', () => {
        console.log('Client disconnected');
        this.clients.delete(ws);
      });

      ws.on('error', (error) => {
        console.error('WebSocket error:', error);
        this.clients.delete(ws);
      });

      ws.on('pong', () => {
        (ws as any).isAlive = true;
      });
    });

    this.wss.on('error', (error) => {
      console.error('WebSocket server error:', error);
    });
  }

  private setupDataProcessor(): void {
    this.dataProcessor.on('depth', (data) => {
      this.broadcast({
        type: 'depth',
        data,
        timestamp: Date.now(),
      });
    });

    this.dataProcessor.on('kline', (data) => {
      this.broadcast({
        type: 'kline',
        data,
        timestamp: Date.now(),
      });
    });

    this.dataProcessor.on('price', (data) => {
      this.broadcast({
        type: 'price',
        data,
        timestamp: Date.now(),
      });
      
      const stats = this.dataProcessor.getMarketStats();
      if (stats) {
        this.broadcast({
          type: 'stats',
          data: stats,
          timestamp: Date.now(),
        });
      }
    });

    this.dataProcessor.on('trade', (data) => {
      this.broadcast({
        type: 'trade',
        data,
        timestamp: Date.now(),
      });
    });

    this.dataProcessor.on('error', (error) => {
      console.error('Data processor error:', error);
      this.broadcast({
        type: 'error',
        data: { message: error.message },
        timestamp: Date.now(),
      });
    });
  }

  private sendInitialData(ws: WebSocket): void {
    try {
      const depth = this.dataProcessor.getLatestDepth();
      if (depth) {
        this.sendToClient(ws, {
          type: 'depth',
          data: depth,
          timestamp: Date.now(),
        });
      }

      const intervals = ['1m', '3m', '5m', '15m'] as const;
      intervals.forEach(interval => {
        const klineData = this.dataProcessor.getLatestKline(interval);
        if (klineData.length > 0) {
          this.sendToClient(ws, {
            type: 'kline',
            data: { interval, data: klineData },
            timestamp: Date.now(),
          });
        }
      });

      const trades = this.dataProcessor.getLatestTrades();
      if (trades.length > 0) {
        this.sendToClient(ws, {
          type: 'trade',
          data: trades,
          timestamp: Date.now(),
        });
      }

      const stats = this.dataProcessor.getMarketStats();
      if (stats) {
        this.sendToClient(ws, {
          type: 'stats',
          data: stats,
          timestamp: Date.now(),
        });
      }
    } catch (error) {
      console.error('Error sending initial data:', error);
    }
  }

  private handleClientMessage(ws: WebSocket, message: any): void {
    switch (message.type) {
      case 'ping':
        this.sendToClient(ws, {
          type: 'pong',
          data: { timestamp: Date.now() },
          timestamp: Date.now(),
        });
        break;
      
      case 'subscribe':
        // Handle subscription requests if needed
        break;
      
      case 'unsubscribe':
        // Handle unsubscription requests if needed
        break;
      
      default:
        console.log('Unknown message type:', message.type);
    }
  }

  private broadcast(message: WebSocketMessage): void {
    const messageStr = JSON.stringify(message);
    
    this.clients.forEach(client => {
      if (client.readyState === WebSocket.OPEN) {
        try {
          client.send(messageStr);
        } catch (error) {
          console.error('Error sending message to client:', error);
          this.clients.delete(client);
        }
      }
    });
  }

  private sendToClient(ws: WebSocket, message: WebSocketMessage): void {
    if (ws.readyState === WebSocket.OPEN) {
      try {
        ws.send(JSON.stringify(message));
      } catch (error) {
        console.error('Error sending message to client:', error);
      }
    }
  }

  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      this.clients.forEach(ws => {
        if ((ws as any).isAlive === false) {
          this.clients.delete(ws);
          return ws.terminate();
        }

        (ws as any).isAlive = false;
        ws.ping();
      });
    }, 30000); // 30 seconds
  }

  public getStats(): {
    connectedClients: number;
    uptime: number;
  } {
    return {
      connectedClients: this.clients.size,
      uptime: process.uptime(),
    };
  }

  public close(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }

    this.clients.forEach(client => {
      client.close();
    });
    
    this.wss.close();
    this.dataProcessor.destroy();
    
    console.log('Dashboard WebSocket server closed');
  }
}
