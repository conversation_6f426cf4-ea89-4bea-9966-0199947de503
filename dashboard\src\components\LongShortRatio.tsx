'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  TimeScale,
} from 'chart.js';
import 'chartjs-adapter-date-fns';
import { OrderBookData, TimeFrame } from '@/types/market';
import { format } from 'date-fns';
import { TrendingUp, TrendingDown, BarChart3 } from 'lucide-react';
import { useChart } from '@/context/ChartContext';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  TimeScale
);

interface LongShortRatioProps {
  orderBook: OrderBookData | null;
  timeframe: TimeFrame;  // Required - synchronized with global timeframe
  isLoading?: boolean;
}

interface RatioDataPoint {
  timestamp: number;
  ratio: number;
  longLiquidity: number;
  shortLiquidity: number;
}

interface RatioData {
  current: number;
  change: number;
  changePercent: number;
  longLiquidity: number;
  shortLiquidity: number;
  sentiment: 'bullish' | 'bearish' | 'neutral';
  chartData: RatioDataPoint[];
}

// Timeframe configuration synchronized with global dashboard timeframes
interface TimeframeConfig {
  label: string;
  value: TimeFrame;
  dataPoints: number;
  updateInterval: number;
}

const TIMEFRAME_CONFIGS: Record<TimeFrame, TimeframeConfig> = {
  '1m': { label: '1M', value: '1m', dataPoints: 60, updateInterval: 60000 },   // 1 minute intervals
  '3m': { label: '3M', value: '3m', dataPoints: 40, updateInterval: 180000 },  // 3 minute intervals
  '5m': { label: '5M', value: '5m', dataPoints: 36, updateInterval: 300000 },  // 5 minute intervals
  '15m': { label: '15M', value: '15m', dataPoints: 32, updateInterval: 900000 }, // 15 minute intervals
};

export default function LongShortRatio({
  orderBook,
  timeframe,
  isLoading = false
}: LongShortRatioProps) {
  const { viewport } = useChart();
  const [ratioData, setRatioData] = useState<RatioData>({
    current: 1,
    change: 0,
    changePercent: 0,
    longLiquidity: 0,
    shortLiquidity: 0,
    sentiment: 'neutral',
    chartData: [],
  });
  const [historicalData, setHistoricalData] = useState<RatioDataPoint[]>([]);

  // Reset historical data when timeframe changes
  useEffect(() => {
    setHistoricalData([]);
  }, [timeframe]);

  // Debug logging to check if we're receiving order book data
  useEffect(() => {
    if (orderBook) {
      console.log('LongShortRatio received orderBook:', {
        bidsCount: orderBook.bids?.length || 0,
        asksCount: orderBook.asks?.length || 0,
        timestamp: orderBook.timestamp
      });
    } else {
      console.log('LongShortRatio: No orderBook data received');
    }
  }, [orderBook]);

  // Calculate current ratio from order book
  // Note: This represents market sentiment through order book liquidity distribution
  // Bid liquidity (long interest) vs Ask liquidity (short interest)
  const currentRatioPoint = useMemo((): RatioDataPoint | null => {
    if (!orderBook || !orderBook.bids || !orderBook.asks ||
        !Array.isArray(orderBook.bids) || !Array.isArray(orderBook.asks) ||
        orderBook.bids.length === 0 || orderBook.asks.length === 0) {
      console.log('LongShortRatio: Invalid order book data', {
        hasOrderBook: !!orderBook,
        hasBids: !!orderBook?.bids,
        hasAsks: !!orderBook?.asks,
        bidsLength: orderBook?.bids?.length || 0,
        asksLength: orderBook?.asks?.length || 0
      });
      return null;
    }

    // Calculate weighted liquidity (price * size) for better representation
    // Focus on top 10 levels for more accurate immediate market sentiment
    const topBids = orderBook.bids.slice(0, Math.min(10, orderBook.bids.length));
    const topAsks = orderBook.asks.slice(0, Math.min(10, orderBook.asks.length));

    const longLiquidity = topBids.reduce((total, level) => {
      const value = level.price * level.size;
      return total + (isNaN(value) ? 0 : value);
    }, 0);

    const shortLiquidity = topAsks.reduce((total, level) => {
      const value = level.price * level.size;
      return total + (isNaN(value) ? 0 : value);
    }, 0);

    // Avoid division by zero and ensure meaningful ratio
    const ratio = shortLiquidity > 0 ? longLiquidity / shortLiquidity : 1;

    console.log('LongShortRatio: Calculated ratio', {
      longLiquidity,
      shortLiquidity,
      ratio,
      timestamp: orderBook.timestamp
    });

    return {
      timestamp: orderBook.timestamp || Date.now(),
      ratio: isNaN(ratio) ? 1 : ratio,
      longLiquidity: isNaN(longLiquidity) ? 0 : longLiquidity,
      shortLiquidity: isNaN(shortLiquidity) ? 0 : shortLiquidity,
    };
  }, [orderBook]);

  // Update historical data with proper time-based aggregation
  useEffect(() => {
    if (!currentRatioPoint) return;

    const config = TIMEFRAME_CONFIGS[timeframe];
    if (!config) return;

    setHistoricalData(prev => {
      // Time-based aggregation: only add if enough time has passed
      const lastPoint = prev[prev.length - 1];
      const timeDiff = lastPoint ? currentRatioPoint.timestamp - lastPoint.timestamp : config.updateInterval;

      // Only add new point if enough time has passed for the selected timeframe
      if (timeDiff < config.updateInterval) {
        // Update the last point instead of adding a new one
        if (lastPoint) {
          const updatedData = [...prev];
          updatedData[updatedData.length - 1] = currentRatioPoint;
          return updatedData;
        }
        return prev;
      }

      const newData = [...prev, currentRatioPoint];

      // Keep only data points within the selected timeframe
      const cutoffTime = Date.now() - (config.dataPoints * config.updateInterval);
      const filteredData = newData.filter(point => point.timestamp > cutoffTime);

      // Limit to max data points for performance
      return filteredData.slice(-config.dataPoints);
    });
  }, [currentRatioPoint, timeframe]);

  // Calculate ratio statistics
  useEffect(() => {
    if (historicalData.length === 0) return;

    const current = historicalData[historicalData.length - 1];
    const previous = historicalData.length > 1 ? historicalData[historicalData.length - 2] : current;
    
    const change = current.ratio - previous.ratio;
    const changePercent = (change / previous.ratio) * 100;
    
    let sentiment: 'bullish' | 'bearish' | 'neutral' = 'neutral';
    if (current.ratio > 1.2) sentiment = 'bullish';
    else if (current.ratio < 0.8) sentiment = 'bearish';

    setRatioData({
      current: current.ratio,
      change,
      changePercent,
      longLiquidity: current.longLiquidity,
      shortLiquidity: current.shortLiquidity,
      sentiment,
      chartData: historicalData,
    });
  }, [historicalData]);

  const formatCurrency = (value: number): string => {
    if (value >= 1000000) {
      return `$${(value / 1000000).toFixed(2)}M`;
    } else if (value >= 1000) {
      return `$${(value / 1000).toFixed(2)}K`;
    }
    return `$${value.toFixed(2)}`;
  };

  const getSentimentColor = (): string => {
    switch (ratioData.sentiment) {
      case 'bullish': return 'text-success-600';
      case 'bearish': return 'text-danger-600';
      default: return 'text-gray-600';
    }
  };

  const getSentimentText = (): string => {
    switch (ratioData.sentiment) {
      case 'bullish': return 'Long Heavy';
      case 'bearish': return 'Short Heavy';
      default: return 'Balanced';
    }
  };

  const getChangeColor = (): string => {
    if (ratioData.change > 0) return 'text-success-600';
    if (ratioData.change < 0) return 'text-danger-600';
    return 'text-gray-600';
  };

  const chartData = {
    labels: ratioData.chartData.map(point => new Date(point.timestamp)),
    datasets: [
      {
        label: 'Long/Short Ratio',
        data: ratioData.chartData.map(point => point.ratio),
        borderColor: ratioData.sentiment === 'bullish' ? '#10b981' : 
                    ratioData.sentiment === 'bearish' ? '#ef4444' : '#6b7280',
        backgroundColor: ratioData.sentiment === 'bullish' ? 'rgba(16, 185, 129, 0.1)' : 
                        ratioData.sentiment === 'bearish' ? 'rgba(239, 68, 68, 0.1)' : 'rgba(107, 114, 128, 0.1)',
        borderWidth: 2,
        fill: true,
        tension: 0.1,
        pointRadius: 0,
        pointHoverRadius: 4,
      },
    ],
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      x: {
        type: 'time' as const,
        time: {
          displayFormats: {
            minute: 'HH:mm',
            hour: 'HH:mm',
            day: 'MMM dd',
          },
        },
        grid: {
          display: false,
        },
        // Synchronize with main chart viewport
        min: viewport.timeframe === timeframe && viewport.min !== null ? viewport.min : undefined,
        max: viewport.timeframe === timeframe && viewport.max !== null ? viewport.max : undefined,
      },
      y: {
        beginAtZero: false,
        grid: {
          color: 'rgba(0, 0, 0, 0.1)',
        },
        ticks: {
          callback: function(value: any) {
            return `${Number(value).toFixed(2)}:1`;
          },
        },
      },
    },
    plugins: {
      legend: {
        display: false,
      },
      title: {
        display: false,
      },
      tooltip: {
        callbacks: {
          title: (context: any) => {
            return format(new Date(context[0].parsed.x), 'MMM dd, HH:mm:ss');
          },
          label: (context: any) => {
            const dataPoint = ratioData.chartData[context.dataIndex];
            if (!dataPoint) return '';

            return [
              `Ratio: ${dataPoint.ratio.toFixed(2)}:1`,
              `Long: ${formatCurrency(dataPoint.longLiquidity)}`,
              `Short: ${formatCurrency(dataPoint.shortLiquidity)}`,
            ];
          },
        },
      },
    },
    animation: {
      duration: 0,
    },
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">
          Long/Short Ratio
        </h3>

        {/* Timeframe Display - synchronized with global timeframe */}
        <div className="text-xs text-gray-500">
          {TIMEFRAME_CONFIGS[timeframe].label} • {ratioData.chartData.length} periods
        </div>
      </div>

      {/* Current Ratio Display */}
      <div className="mb-4">
        <div className="flex items-center space-x-2">
          <div className="text-2xl font-bold text-gray-900">
            {ratioData.current.toFixed(2)}:1
          </div>
          <div className={`flex items-center text-sm font-medium ${getChangeColor()}`}>
            {ratioData.change > 0 ? (
              <TrendingUp className="h-4 w-4 mr-1" />
            ) : ratioData.change < 0 ? (
              <TrendingDown className="h-4 w-4 mr-1" />
            ) : null}
            {ratioData.change !== 0 && (
              <span>
                {ratioData.change > 0 ? '+' : ''}{ratioData.change.toFixed(3)} 
                ({ratioData.changePercent > 0 ? '+' : ''}{ratioData.changePercent.toFixed(2)}%)
              </span>
            )}
          </div>
        </div>
        <div className={`text-sm font-medium ${getSentimentColor()}`}>
          {getSentimentText()}
        </div>
      </div>

      {/* Chart */}
      <div className="relative h-64 mb-4">
        {isLoading && (
          <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
          </div>
        )}
        
        {ratioData.chartData.length > 0 ? (
          <Line data={chartData} options={chartOptions} />
        ) : (
          <div className="flex items-center justify-center h-full text-gray-500">
            <div className="text-center">
              <BarChart3 className="h-8 w-8 mx-auto mb-2 text-gray-400" />
              <p className="text-sm">Loading ratio data...</p>
            </div>
          </div>
        )}
      </div>

      {/* Liquidity Breakdown */}
      <div className="grid grid-cols-2 gap-4 pt-3 border-t border-gray-200">
        <div className="text-center">
          <div className="text-sm text-gray-500 mb-1">Long Liquidity</div>
          <div className="text-lg font-semibold text-success-600">
            {formatCurrency(ratioData.longLiquidity)}
          </div>
        </div>
        <div className="text-center">
          <div className="text-sm text-gray-500 mb-1">Short Liquidity</div>
          <div className="text-lg font-semibold text-danger-600">
            {formatCurrency(ratioData.shortLiquidity)}
          </div>
        </div>
      </div>
    </div>
  );
}
