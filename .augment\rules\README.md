---
type: "always_apply"
---

# Rules System

### Description

Rules for working with the Bitunix Trading Application covering documentation, development, API integration, and system maintenance.

### Rules Files

| File | Purpose | Focus |
| --- | --- | --- |
| [Documentation Standards](./documentation-standards.md) | Documentation creation and maintenance | Format, structure, validation |
| [Bitunix API Integration](./bitunix-api-integration.md) | API implementation and validation | Authentication, rate limits, errors |
| [Application Development](./application-development.md) | Development workflow and standards | Performance, testing, integration |
| [Template Usage](./template-usage.md) | Template application and maintenance | Template selection, quality checks |
| [Data Validation](./data-validation.md) | Data integrity and type safety | Schema validation, error handling |
| [Performance Optimization](./performance-optimization.md) | Performance monitoring and optimization | Resource limits, monitoring |
| [Error Handling](./error-handling.md) | Error recovery and system stability | Error classification, recovery protocols |

### Usage Guide

| Task | Primary Rule | Secondary Rule |
| --- | --- | --- |
| Create documentation | [Template Usage](./template-usage.md) | [Documentation Standards](./documentation-standards.md) |
| Implement API | [Bitunix API Integration](./bitunix-api-integration.md) | [Error Handling](./error-handling.md) |
| Develop code | [Application Development](./application-development.md) | [Performance Optimization](./performance-optimization.md) |
| Validate data | [Data Validation](./data-validation.md) | [Error Handling](./error-handling.md) |
| Optimize performance | [Performance Optimization](./performance-optimization.md) | [Application Development](./application-development.md) |
| Handle errors | [Error Handling](./error-handling.md) | [Data Validation](./data-validation.md) |

### Rule Priority

| Conflict | Resolution |
| --- | --- |
| Rules conflict | Use most specific rule |
| Code vs docs | Code implementation |
| Old vs new | Most recent version |
| User vs system | Clarify requirements |

### Quick Reference

| Need | Rule File | Key Section |
| --- | --- | --- |
| Create docs | [Template Usage](./template-usage.md) | Template Selection |
| Handle API | [Bitunix API Integration](./bitunix-api-integration.md) | Error Handling |
| Validate data | [Data Validation](./data-validation.md) | Schema Validation |
| Fix errors | [Error Handling](./error-handling.md) | Recovery Protocols |
| Optimize speed | [Performance Optimization](./performance-optimization.md) | Performance Targets |
| Check quality | [Documentation Standards](./documentation-standards.md) | Validation Rules |
