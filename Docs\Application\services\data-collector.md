# Data Collector Service

Rate Limit: 10 req/sec/ip [api](../../Bitunix/common/introduction.md#rate-limits)

### Description [src](../../src/services/BitunixDataCollector.ts)

Main orchestrator for data collection operations. Coordinates REST API calls, WebSocket connections, and data storage.

### Configuration Parameters [src](../../src/config/config.ts#DataCollectorConfig)

| Parameter | Type | Required | Description |
| --- | --- | --- | --- |
| symbol | string | Yes | Trading pair, e.g. ETHUSDT |
| klineIntervals | KlineInterval[] [→](../data-formats/market-data.md#timeframe) | Yes | Timeframes ['1m', '3m', '5m', '15m'] |
| depthLimit | string | Yes | Order book depth ('50') [api](../../Bitunix/market/get-depth.md) |
| depthCollectionInterval | number | Yes | Order book interval (1000ms) |
| klineCollectionInterval | number | Yes | Kline interval (5000ms) |
| enableWebSocket | boolean | Yes | Enable real-time data [ws](./websocket-client.md) |
| enableRestApi | boolean | Yes | Enable REST collection [api](./api-client.md) |

Usage Example:

```typescript
const collectorConfig: DataCollectorConfig = {
  symbol: 'ETHUSDT',
  klineIntervals: ['1m', '3m', '5m', '15m'],
  depthLimit: '50',
  depthCollectionInterval: 1000,
  klineCollectionInterval: 5000,
  enableWebSocket: true,
  enableRestApi: true,
};

const dataCollector = new BitunixDataCollector(bitunixConfig, collectorConfig, './data');
await dataCollector.start();
```

### Events [src](../../src/services/BitunixDataCollector.ts#events)

| Event | Parameters | Description |
| --- | --- | --- |
| started | none | Collection started |
| stopped | none | Collection stopped |
| dataCollected | type, data [→](../data-formats/market-data.md) | Data collected |
| error | error | Collection error |
