# Long/Short Ratio Component

### Description [src](../../dashboard/src/components/LongShortRatio.tsx)

Real-time visualization of long vs short position ratio as a time-series line chart. Displays market sentiment through liquidity analysis with configurable time periods.

### Props [src](../../dashboard/src/components/LongShortRatio.tsx#LongShortRatioProps)

| Parameter | Type | Required | Description |
| --- | --- | --- | --- |
| orderBook | OrderBookData \| null [→](../data-formats/market-data.md#orderbookdata) | Yes | Order book data for ratio calculation |
| timeframe | TimeFrame [→](../data-formats/market-data.md#timeframe) | No | Current timeframe (default: '1m') |
| isLoading | boolean | No | Loading state indicator |

Usage Example:
```typescript
<LongShortRatio 
  orderBook={orderBookData}
  timeframe="5m"
  isLoading={false}
/>
```

### Period Selection [src](../../dashboard/src/components/LongShortRatio.tsx#PeriodConfig)

| Period | Label | Data Points | Update Interval | Use Case |
| --- | --- | --- | --- | --- |
| 1m | 1M | 60 | 1 second | Short-term scalping |
| 5m | 5M | 48 | 5 seconds | Intraday trading |
| 15m | 15M | 32 | 15 seconds | Swing analysis |
| 1h | 1H | 24 | 1 minute | Trend analysis |
| 4h | 4H | 18 | 4 minutes | Position analysis |
| 1d | 1D | 14 | 24 minutes | Long-term sentiment |

### Ratio Calculation [src](../../dashboard/src/components/LongShortRatio.tsx#calculation)

```typescript
// Long liquidity (bid side - represents long positions)
const longLiquidity = orderBook.bids.reduce((total, level) => {
  return total + (level.price * level.size);
}, 0);

// Short liquidity (ask side - represents short positions)  
const shortLiquidity = orderBook.asks.reduce((total, level) => {
  return total + (level.price * level.size);
}, 0);

// Long/Short ratio
const ratio = longLiquidity / (shortLiquidity || 1);
```

### Market Sentiment [src](../../dashboard/src/components/LongShortRatio.tsx#sentiment)

| Ratio Range | Sentiment | Color | Interpretation |
| --- | --- | --- | --- |
| > 1.2 | Bullish | Green | Long Heavy - More buying pressure |
| 0.8 - 1.2 | Neutral | Gray | Balanced - Equal buying/selling pressure |
| < 0.8 | Bearish | Red | Short Heavy - More selling pressure |

### Data Output [src](../../dashboard/src/components/LongShortRatio.tsx#RatioData)

| Field | Type | Description |
| --- | --- | --- |
| current | number | Current long/short ratio |
| change | number | Change from previous period |
| changePercent | number | Percentage change |
| longLiquidity | number | Total long position liquidity (USD) |
| shortLiquidity | number | Total short position liquidity (USD) |
| sentiment | 'bullish' \| 'bearish' \| 'neutral' | Market sentiment classification |
| chartData | RatioDataPoint[] | Historical ratio data points |

### Chart Features [src](../../dashboard/src/components/LongShortRatio.tsx#chartOptions)

| Feature | Description |
| --- | --- |
| Time-series Line Chart | Real-time ratio visualization using Chart.js |
| Dynamic Coloring | Chart color changes based on sentiment |
| Interactive Tooltips | Detailed ratio, long, and short liquidity info |
| Responsive Design | Adapts to container size |
| Performance Optimized | Zero animation duration for real-time updates |

### Extensible Architecture [src](../../dashboard/src/components/LongShortRatio.tsx#extensible)

The component is designed for future enhancements:

#### Moving Averages Support
```typescript
// Future enhancement structure
interface MAConfig {
  period: number;
  type: 'SMA' | 'EMA' | 'WMA';
  color: string;
}

// Can be added to props
maOverlays?: MAConfig[];
```

#### Multiple Ratio Calculations
```typescript
// Future enhancement structure  
interface RatioConfig {
  type: 'liquidity' | 'volume' | 'orders';
  calculation: (orderBook: OrderBookData) => number;
  label: string;
}

// Can be added to props
ratioTypes?: RatioConfig[];
```

#### Technical Indicators Integration
```typescript
// Future enhancement structure
interface IndicatorConfig {
  type: 'RSI' | 'MACD' | 'Bollinger';
  params: Record<string, number>;
  overlay: boolean;
}

// Can be added to props
indicators?: IndicatorConfig[];
```

### Integration [src](../../dashboard/src/components/Dashboard.tsx#integration)

The component integrates seamlessly with the dashboard:

```typescript
{/* Long/Short Ratio Row */}
<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
  <div>
    <LongShortRatio 
      orderBook={orderBookData}
      timeframe={dashboardState.selectedTimeframe}
      isLoading={isLoading && !orderBookData}
    />
  </div>
  
  {/* Space for future technical indicators */}
  <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
    {/* Future MA overlays or other indicators */}
  </div>
</div>
```

### Performance Considerations [src](../../dashboard/src/components/LongShortRatio.tsx#performance)

| Optimization | Implementation |
| --- | --- |
| Data Point Limiting | Max data points per period for memory efficiency |
| Update Throttling | Period-based update intervals prevent excessive renders |
| Memoized Calculations | useMemo for expensive ratio calculations |
| Chart Performance | Zero animation duration for real-time updates |
| Memory Management | Automatic cleanup of old data points |

### Styling & Theming [src](../../dashboard/src/components/LongShortRatio.tsx#styling)

Follows existing dashboard component patterns:
- Consistent card layout with rounded corners and shadows
- Tailwind CSS utility classes for responsive design
- Color scheme matches other dashboard components
- Loading states with spinner animations
- Hover effects and interactive elements

### Future Enhancements

1. **Custom Moving Averages**: Add SMA, EMA, WMA overlays
2. **Multiple Ratio Types**: Volume-based, order count-based ratios
3. **Alert System**: Notifications for extreme ratio levels
4. **Export Functionality**: CSV/JSON data export
5. **Comparison Mode**: Multiple timeframe comparison
6. **Historical Analysis**: Extended historical data storage
