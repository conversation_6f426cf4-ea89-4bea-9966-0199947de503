'use client'

import React, { useState, useEffect, useRef } from 'react'
import { Trade } from '@/types/market'
import { format } from 'date-fns'
import { TrendingUp, TrendingDown } from 'lucide-react'

interface TradeFeedProps {
  trades: Trade[]
  isLoading?: boolean
}

interface TradeRowProps {
  trade: Trade
  isNew?: boolean
}

function TradeRow({ trade, isNew }: TradeRowProps) {
  const [isClient, setIsClient] = useState(false)
  const isBuy = trade.side === 'buy'
  const sideColor = isBuy ? 'text-success-600' : 'text-danger-600'
  const bgColor = isBuy ? 'bg-success-50' : 'bg-danger-50'

  useEffect(() => {
    setIsClient(true)
  }, [])

  return (
    <div
      className={`flex items-center justify-between py-2 px-3 text-sm border-b border-gray-100 transition-all duration-300 ${
        isNew ? (isBuy ? 'flash-green' : 'flash-red') : ''
      }`}
    >
      <div className="flex items-center space-x-2">
        <div className={`p-1 rounded-full ${bgColor}`}>
          {isBuy ? (
            <TrendingUp className={`h-3 w-3 ${sideColor}`} />
          ) : (
            <TrendingDown className={`h-3 w-3 ${sideColor}`} />
          )}
        </div>
        <span className={`font-medium ${sideColor} uppercase text-xs`}>
          {trade.side}
        </span>
      </div>

      <div className="text-right">
        <div className={`font-mono font-medium ${sideColor}`}>
          ${trade.price.toFixed(2)}
        </div>
        <div className="text-gray-500 text-xs">
          {trade.volume.toFixed(4)} ETH
        </div>
      </div>

      <div className="text-right text-xs text-gray-500">
        <div>{isClient ? format(new Date(trade.timestamp), 'HH:mm:ss') : '--:--:--'}</div>
        <div className="text-gray-400">
          ${(trade.price * trade.volume).toFixed(2)}
        </div>
      </div>
    </div>
  )
}

export default function TradeFeed({ trades, isLoading = false }: TradeFeedProps) {
  const [isClient, setIsClient] = useState(false)
  const [newTradeIds, setNewTradeIds] = useState<Set<string>>(new Set())
  const [prevTradeCount, setPrevTradeCount] = useState(0)
  const scrollRef = useRef<HTMLDivElement>(null)
  const [autoScroll, setAutoScroll] = useState(true)

  useEffect(() => {
    setIsClient(true)
  }, [])

  useEffect(() => {
    if (trades.length > prevTradeCount) {
      const newIds = new Set<string>()
      const newTrades = trades.slice(0, trades.length - prevTradeCount)
      newTrades.forEach(trade => newIds.add(trade.id))
      
      setNewTradeIds(newIds)
      
      if (autoScroll && scrollRef.current) {
        scrollRef.current.scrollTop = 0
      }
      
      setTimeout(() => setNewTradeIds(new Set()), 1000)
    }
    
    setPrevTradeCount(trades.length)
  }, [trades, prevTradeCount, autoScroll])

  const handleScroll = () => {
    if (scrollRef.current) {
      const { scrollTop } = scrollRef.current
      setAutoScroll(scrollTop < 50)
    }
  }

  const recentTrades = trades.slice(0, 20)
  const buyTrades = recentTrades.filter(t => t.side === 'buy')
  const sellTrades = recentTrades.filter(t => t.side === 'sell')
  const buyVolume = buyTrades.reduce((sum, t) => sum + t.volume, 0)
  const sellVolume = sellTrades.reduce((sum, t) => sum + t.volume, 0)
  const totalVolume = buyVolume + sellVolume
  const buyPercentage = totalVolume > 0 ? (buyVolume / totalVolume) * 100 : 50

  return (
    <div className="card">
      <div className="card-header">
        <h3 className="card-title">Recent Trades</h3>
        <div className="flex items-center space-x-4 text-sm">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-success-500 rounded-full"></div>
            <span className="text-success-600 font-medium">
              {buyPercentage.toFixed(1)}% Buy
            </span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-danger-500 rounded-full"></div>
            <span className="text-danger-600 font-medium">
              {(100 - buyPercentage).toFixed(1)}% Sell
            </span>
          </div>
        </div>
      </div>

      {isLoading && (
        <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
        </div>
      )}

      <div className="space-y-2">
        {/* Volume distribution bar */}
        <div className="flex h-2 bg-gray-200 rounded-full overflow-hidden">
          <div 
            className="bg-success-500 transition-all duration-500"
            style={{ width: `${buyPercentage}%` }}
          />
          <div 
            className="bg-danger-500 transition-all duration-500"
            style={{ width: `${100 - buyPercentage}%` }}
          />
        </div>

        {/* Header */}
        <div className="flex justify-between text-xs font-medium text-gray-500 uppercase tracking-wide px-3">
          <span>Side</span>
          <span>Price / Size</span>
          <span>Time / Value</span>
        </div>

        {/* Trades list */}
        <div 
          ref={scrollRef}
          onScroll={handleScroll}
          className="h-96 overflow-y-auto scrollbar-thin"
        >
          {trades.length > 0 ? (
            trades.map((trade, index) => (
              <TradeRow
                key={`${trade.id}_${trade.timestamp}_${index}`}
                trade={trade}
                isNew={newTradeIds.has(trade.id)}
              />
            ))
          ) : (
            <div className="flex items-center justify-center h-full text-gray-500">
              <div className="text-center">
                <div className="animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-32 mx-auto mb-2"></div>
                  <div className="h-4 bg-gray-200 rounded w-24 mx-auto"></div>
                </div>
                <p className="mt-4">Loading trades...</p>
              </div>
            </div>
          )}
        </div>

        {/* Auto-scroll indicator */}
        {!autoScroll && (
          <div className="flex justify-center">
            <button
              onClick={() => {
                setAutoScroll(true)
                if (scrollRef.current) {
                  scrollRef.current.scrollTop = 0
                }
              }}
              className="text-xs text-primary-600 hover:text-primary-700 font-medium"
            >
              ↑ Scroll to top for auto-updates
            </button>
          </div>
        )}
      </div>
    </div>
  )
}
