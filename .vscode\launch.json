{"version": "0.2.0", "configurations": [{"name": "Launch Bitunix Data Collector", "type": "node", "request": "launch", "program": "${workspaceFolder}/dist/index.js", "preLaunchTask": "npm: build", "outFiles": ["${workspaceFolder}/dist/**/*.js"], "envFile": "${workspaceFolder}/.env", "console": "integratedTerminal", "internalConsoleOptions": "neverOpen"}, {"name": "Launch Dev Server", "type": "node", "request": "launch", "runtimeExecutable": "npm", "runtimeArgs": ["run", "dev"], "envFile": "${workspaceFolder}/.env", "console": "integratedTerminal", "internalConsoleOptions": "neverOpen"}]}