import { NextRequest, NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import path from 'path';
import { z } from 'zod';

// Request validation schema
const MarketDataRequestSchema = z.object({
  symbol: z.string().default('ETHUSDT'),
  timeframe: z.enum(['1m', '3m', '5m', '15m']).default('1m'),
  limit: z.number().min(1).max(1000).default(500),
});

interface KlineData {
  time: string;
  open: string;
  high: string;
  low: string;
  close: string;
  volume: string;
}

interface TradeData {
  price: string;
  size: string;
  side: 'buy' | 'sell';
  timestamp: number;
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const params = MarketDataRequestSchema.parse({
      symbol: searchParams.get('symbol') || 'ETHUSDT',
      timeframe: searchParams.get('timeframe') || '1m',
      limit: parseInt(searchParams.get('limit') || '500'),
    });

    const klineData = await readKlineData(params.symbol, params.timeframe, params.limit);
    const tradeData = await readTradeData(params.symbol);
    const chartData = processChartData(klineData, tradeData);

    return NextResponse.json(chartData);
  } catch (error) {
    console.error('Market data API error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch market data' },
      { status: 500 }
    );
  }
}

async function readKlineData(symbol: string, timeframe: string, limit: number): Promise<KlineData[]> {
  try {
    const dataDir = path.join(process.cwd(), '..', 'data', 'kline');
    const fileName = `${symbol}_${timeframe}.json`;
    const filePath = path.join(dataDir, fileName);
    
    const fileContent = await fs.readFile(filePath, 'utf-8');
    const lines = fileContent.trim().split('\n').filter(line => line.trim());

    // Parse each line as individual NDJSON objects
    const allData: any[] = [];
    for (const line of lines) {
      try {
        const lineData = JSON.parse(line);
        allData.push(lineData);
      } catch (error) {
        console.warn('Failed to parse kline data line:', line);
      }
    }

    // Remove duplicates by time and get the last N entries
    const uniqueData = new Map<string, any>();
    for (const data of allData) {
      uniqueData.set(data.time, data);
    }

    // Convert to array, sort by time, and take last N entries
    const sortedData = Array.from(uniqueData.values())
      .sort((a, b) => parseInt(a.time) - parseInt(b.time));

    const klineData: KlineData[] = sortedData
      .slice(-limit)
      .map(data => ({
        time: data.time,
        open: data.open,
        high: data.high,
        low: data.low,
        close: data.close,
        volume: data.quoteVol || data.volume || '0',
      }));
    
    return klineData;
  } catch (error) {
    console.error('Failed to read kline data:', error);
    return [];
  }
}

async function readTradeData(symbol: string): Promise<TradeData[]> {
  try {
    const dataDir = path.join(process.cwd(), '..', 'data', 'trades');
    const fileName = `${symbol}.json`;
    const filePath = path.join(dataDir, fileName);

    const fileContent = await fs.readFile(filePath, 'utf-8');
    const lines = fileContent.trim().split('\n').filter(line => line.trim());

    // Parse newline-delimited JSON format
    const tradeData: TradeData[] = [];
    const startIndex = Math.max(0, lines.length - 100); // Get last 100 trade batches

    for (let i = startIndex; i < lines.length; i++) {
      try {
        const data = JSON.parse(lines[i]);
        // Extract individual trades from the batch
        if (data.trades && Array.isArray(data.trades)) {
          for (const trade of data.trades) {
            tradeData.push({
              price: trade.p || '0',
              size: trade.v || '0',
              side: trade.s || 'buy',
              timestamp: new Date(trade.t).getTime() || data.timestamp || Date.now(),
            });
          }
        }
      } catch (parseError) {
        console.warn('Failed to parse trade data line:', lines[i]);
      }
    }

    return tradeData;
  } catch (error) {
    console.error('Failed to read trade data:', error);
    return [];
  }
}

function processChartData(klineData: KlineData[], tradeData: TradeData[]) {
  // Ensure data is unique and sorted by time
  const uniqueKlineData = new Map<number, KlineData>();
  for (const candle of klineData) {
    const timeKey = Math.floor(parseInt(candle.time) / 1000);
    uniqueKlineData.set(timeKey, candle);
  }

  const sortedKlineData = Array.from(uniqueKlineData.values())
    .sort((a, b) => parseInt(a.time) - parseInt(b.time));

  const priceData = sortedKlineData.map(candle => ({
    time: Math.floor(parseInt(candle.time) / 1000), // Convert to seconds
    open: parseFloat(candle.open),
    high: parseFloat(candle.high),
    low: parseFloat(candle.low),
    close: parseFloat(candle.close),
  }));

  const volumeData = sortedKlineData.map(candle => ({
    time: Math.floor(parseInt(candle.time) / 1000),
    value: parseFloat(candle.volume),
  }));

  const cvdData = calculateCVD(tradeData, sortedKlineData);

  return {
    price: priceData,
    volume: volumeData,
    cvd: cvdData,
  };
}

function calculateCVD(tradeData: TradeData[], klineData: KlineData[]) {
  if (tradeData.length === 0 || klineData.length === 0) {
    return klineData.map(candle => ({
      time: Math.floor(parseInt(candle.time) / 1000),
      value: 0,
    }));
  }

  let cumulativeDelta = 0;
  const cvdData: { time: number; value: number }[] = [];

  for (const candle of klineData) {
    const candleTime = parseInt(candle.time);
    const candleEndTime = candleTime + (60 * 1000); // Assuming 1-minute candles
    
    let periodDelta = 0;
    
    for (const trade of tradeData) {
      if (trade.timestamp >= candleTime && trade.timestamp < candleEndTime) {
        const volume = parseFloat(trade.size);
        if (trade.side === 'buy') {
          periodDelta += volume;
        } else {
          periodDelta -= volume;
        }
      }
    }
    
    cumulativeDelta += periodDelta;
    cvdData.push({
      time: Math.floor(candleTime / 1000),
      value: cumulativeDelta,
    });
  }

  return cvdData;
}
