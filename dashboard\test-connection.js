// Simple test script to verify WebSocket connection
const WebSocket = require('ws');

console.log('Testing WebSocket connection to dashboard server...');

const ws = new WebSocket('ws://localhost:8080');

ws.on('open', () => {
  console.log('✅ Connected to WebSocket server');
  
  // Send a ping
  ws.send(JSON.stringify({
    type: 'ping',
    data: {},
    timestamp: Date.now()
  }));
});

ws.on('message', (data) => {
  try {
    const message = JSON.parse(data.toString());
    console.log('📨 Received message:', message.type);
    
    if (message.type === 'pong') {
      console.log('🏓 Pong received - connection is working!');
    } else if (message.type === 'depth') {
      console.log('📊 Order book data received');
    } else if (message.type === 'kline') {
      console.log('📈 Kline data received for interval:', message.data.interval);
    } else if (message.type === 'price') {
      console.log('💰 Price data received:', message.data.data?.mp || message.data.mp);
    } else if (message.type === 'trade') {
      console.log('🔄 Trade data received, trades count:', message.data.length);
    } else if (message.type === 'stats') {
      console.log('📊 Market stats received, current price:', message.data.currentPrice);
    }
  } catch (error) {
    console.error('❌ Error parsing message:', error);
  }
});

ws.on('error', (error) => {
  console.error('❌ WebSocket error:', error);
});

ws.on('close', () => {
  console.log('🔌 WebSocket connection closed');
  process.exit(0);
});

// Close after 10 seconds
setTimeout(() => {
  console.log('⏰ Test completed, closing connection...');
  ws.close();
}, 10000);
