# Order Book Component

### Description [src](../../dashboard/src/components/OrderBook.tsx)

Real-time order book display showing bid and ask levels with spread analysis and liquidity visualization.

### Props [src](../../dashboard/src/components/OrderBook.tsx#OrderBookProps)

| Parameter | Type | Required | Description |
| --- | --- | --- | --- |
| data | OrderBookData [→](../data-formats/market-data.md#orderbookdata) | Yes | Order book bid/ask levels |
| maxLevels | number | No | Maximum levels to display (default: 20) |
| showSpread | boolean | No | Show bid-ask spread (default: true) |
| showTotal | boolean | No | Show cumulative totals (default: true) |

Usage Example:

```typescript
<OrderBook
  data={orderBookData}
  maxLevels={20}
  showSpread={true}
  showTotal={true}
/>
```

### Display Format [src](../../dashboard/src/components/OrderBook.tsx#display)

```
ASK LEVELS (Sell Orders)
Price    | Size    | Total
$2,985.50| 1.234   | 5.678
$2,985.25| 0.567   | 4.444
$2,985.00| 2.345   | 3.877

SPREAD: $0.25 (0.008%)

BID LEVELS (Buy Orders)
$2,984.75| 1.890   | 6.789
$2,984.50| 2.456   | 4.899
$2,984.25| 1.234   | 2.443
```

### Real-Time Updates [src](../../dashboard/src/components/OrderBook.tsx#updates)

| Feature | Description |
| --- | --- |
| Live Data | 1-second updates from WebSocket server [→](../dashboard/websocket-server.md) |
| Price Highlighting | Color-coded price changes |
| Size Animation | Visual feedback for volume changes |
| Spread Calculation | Real-time bid-ask spread monitoring |

### Visualization Features [src](../../dashboard/src/components/OrderBook.tsx#visualization)

| Feature | Description |
| --- | --- |
| Color Coding | Green for bids, red for asks |
| Size Bars | Visual representation of order sizes |
| Cumulative Totals | Running total of liquidity |
| Spread Indicator | Highlighted spread with percentage |

### Market Analysis [src](../../dashboard/src/components/OrderBook.tsx#analysis)

| Metric | Calculation | Description |
| --- | --- | --- |
| Bid-Ask Spread | Best Ask - Best Bid | Market liquidity indicator |
| Total Bid Volume | Sum of all bid sizes | Buy-side liquidity |
| Total Ask Volume | Sum of all ask sizes | Sell-side liquidity |
| Liquidity Ratio | Bid Volume / Ask Volume | Market balance indicator |

### Data Output [src](../../dashboard/src/components/OrderBook.tsx#output)

| Field | Type | Description |
| --- | --- | --- |
| spread | number | Current bid-ask spread |
| spreadPercent | number | Spread as percentage of mid-price |
| totalBidVolume | number | Total bid-side volume |
| totalAskVolume | number | Total ask-side volume |
| midPrice | number | Mid-point between best bid and ask |
