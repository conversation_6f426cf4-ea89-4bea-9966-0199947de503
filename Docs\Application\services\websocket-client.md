# WebSocket Client Service

### Description [src](../../src/services/BitunixWebSocketClient.ts)

Real-time data streaming client for Bitunix WebSocket API with automatic reconnection and channel management.

### Configuration [src](../../src/config/config.ts#WebSocketConfig)

| Parameter | Type | Required | Description |
| --- | --- | --- | --- |
| wsUrl | string | Yes | WebSocket URL (wss://fapi.bitunix.com/public/) [api](../../Bitunix/websocket/prepare/websocket.md) |
| reconnectInterval | number | No | Reconnection interval (default: 5000ms) |
| maxReconnectAttempts | number | No | Max reconnection attempts (default: 10) |
| pingInterval | number | No | Ping interval (default: 30000ms) |

Usage Example:

```typescript
const wsClient = new BitunixWebSocketClient({
  wsUrl: 'wss://fapi.bitunix.com/public/',
  reconnectInterval: 5000,
  maxReconnectAttempts: 10,
});

await wsClient.connect();

// Subscribe to channels
wsClient.subscribe('ETHUSDT', 'trade');
wsClient.subscribe('ETHUSDT', 'price');

// Handle events
wsClient.on('trade', (data) => {
  console.log('Trade data:', data);
});

wsClient.on('price', (data) => {
  console.log('Price data:', data);
});
```

### Subscription Channels [api](../../Bitunix/websocket/)

| Channel | Data Type | Description |
| --- | --- | --- |
| trade [api](../../Bitunix/websocket/public/trade-channel.md) | TradeData | Individual trade executions |
| price [api](../../Bitunix/websocket/public/market-price-channel.md) | MarketPriceData | Real-time price updates |
| depth [api](../../Bitunix/websocket/public/depth-channel.md) | DepthData | Order book depth updates |
| kline [api](../../Bitunix/websocket/public/kline-channel.md) | KlineData | Candlestick data updates |
| ticker [api](../../Bitunix/websocket/public/ticker-channel.md) | TickerData | Market ticker updates |

### Events [src](../../src/services/BitunixWebSocketClient.ts#events)

| Event | Parameters | Description |
| --- | --- | --- |
| connected | none | WebSocket connection established |
| disconnected | none | WebSocket connection closed |
| trade | TradeData [→](../data-formats/market-data.md#trade) | Trade execution received |
| price | MarketPriceData | Price update received |
| depth | DepthData | Order book update received |
| error | Error | Connection or data error |

### Connection Management [src](../../src/services/BitunixWebSocketClient.ts#connection)

| Feature | Description |
| --- | --- |
| Automatic Reconnection | Exponential backoff with max attempts |
| Heartbeat Monitoring | Ping/pong to detect connection health |
| Graceful Shutdown | Clean disconnection with unsubscribe |
| Error Recovery | Automatic resubscription after reconnection |

### Subscription Management [src](../../src/services/BitunixWebSocketClient.ts#subscriptions)

| Method | Parameters | Description |
| --- | --- | --- |
| subscribe | symbol, channel | Subscribe to data channel |
| unsubscribe | symbol, channel | Unsubscribe from data channel |
| unsubscribeAll | none | Unsubscribe from all channels |
| getSubscriptions | none | Get current subscriptions list |
