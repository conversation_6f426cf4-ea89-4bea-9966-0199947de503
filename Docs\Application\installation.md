# Installation Guide

### Description

Setup and deployment guide for the Bitunix Trading Application development and production environments.

### Prerequisites

| Requirement | Version | Description |
| --- | --- | --- |
| Node.js | 18+ | JavaScript runtime with TypeScript support |
| npm | 8+ | Package manager (included with Node.js) |
| Bitunix API Key | - | API credentials from Bitunix exchange [api](../Bitunix/common/introduction.md) |
| Modern Browser | - | For dashboard access (Chrome, Firefox, Safari, Edge) |

### Installation Steps

```bash
# 1. Clone repository
git clone <repository-url>
cd bitunix-trading-app

# 2. Install all dependencies
npm run app:install

# 3. Configure environment
cp .env.example .env.local
# Edit .env.local with your API credentials

# 4. Start development environment
npm run app:dev
```

### Environment Configuration [config](./configuration.md)

| Variable | Required | Description |
| --- | --- | --- |
| BITUNIX_API_KEY | Yes | Your Bitunix API key [api](../Bitunix/common/introduction.md) |
| BITUNIX_SECRET_KEY | Yes | Your Bitunix secret key [api](../Bitunix/common/introduction.md) |
| TRADING_SYMBOL | No | Trading pair (default: ETHUSDT) |
| DASHBOARD_PORT | No | Dashboard port (default: 3001) |
| WEBSOCKET_PORT | No | WebSocket port (default: 8080) |

### Verification

| Service | URL | Expected Result |
| --- | --- | --- |
| Dashboard | http://localhost:3001 | Trading dashboard interface |
| WebSocket | ws://localhost:8080 | Real-time data connection |
| Data Collection | Console output | "Data collection started" message |

### Production Deployment

```bash
# Build for production
npm run app:build

# Start production services
npm run app:start

# Or combined
npm run app:prod
```

### Troubleshooting

| Issue | Solution |
| --- | --- |
| Node.js version | Use Node.js 18+ with `node --version` |
| Port conflicts | Change ports in .env.local or kill conflicting processes |
| API credentials | Verify keys at [Bitunix API Management](https://www.bitunix.com/account/apiManagement) |
| Permission errors | Run `chmod 600 .env.local` on Unix systems |
| Build failures | Run `npm run type-check` to identify TypeScript errors |

### Directory Structure

```
bitunix-trading-app/
├── src/                    # Data collector source code
├── dashboard/              # Next.js dashboard application
├── data/                   # Data storage directory (auto-created)
├── .env.local             # Local environment configuration
└── package.json           # Main application dependencies
```

For detailed configuration: [Configuration Guide](./configuration.md) • [Scripts Guide](./development/scripts.md)
