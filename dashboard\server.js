// Use tsx to run TypeScript directly
require('tsx/cjs');
const { DashboardWebSocketServer } = require('./src/lib/websocketServer.ts');
const path = require('path');

// Configuration
const WS_PORT = process.env.WS_PORT || 8080;
const DATA_DIR = process.env.DATA_DIR || path.resolve(__dirname, '../data');

console.log('Starting Bitunix Dashboard WebSocket Server...');
console.log(`WebSocket Port: ${WS_PORT}`);
console.log(`Data Directory: ${DATA_DIR}`);

// Create and start the WebSocket server
const server = new DashboardWebSocketServer(WS_PORT, DATA_DIR);

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\nReceived SIGINT, shutting down gracefully...');
  server.close();
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\nReceived SIGTERM, shutting down gracefully...');
  server.close();
  process.exit(0);
});

// Error handling
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  server.close();
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  server.close();
  process.exit(1);
});

console.log('Dashboard WebSocket Server is running...');
console.log('Press Ctrl+C to stop the server');

// Keep the process alive
setInterval(() => {
  const stats = server.getStats();
  console.log(`[${new Date().toISOString()}] Connected clients: ${stats.connectedClients}, Uptime: ${Math.floor(stats.uptime)}s`);
}, 30000); // Log stats every 30 seconds
