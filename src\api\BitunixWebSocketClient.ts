import WebSocket from 'ws';
import { EventEmitter } from 'events';
import { logger } from '@/utils/logger';
import { 
  BitunixConfig,
  WebSocketPing,
  WebSocketPong,
  WebSocketSubscribe,
  MarketPriceMessage,
  TradeMessage,
  MarketPriceMessageSchema,
  TradeMessageSchema,
  WebSocketPongSchema
} from '@/types/bitunix';

export interface WebSocketEvents {
  connected: () => void;
  disconnected: () => void;
  error: (error: Error) => void;
  marketPrice: (data: MarketPriceMessage) => void;
  trade: (data: TradeMessage) => void;
  pong: (data: WebSocketPong) => void;
}

export class BitunixWebSocketClient extends EventEmitter {
  private ws: WebSocket | null = null;
  private readonly config: BitunixConfig;
  private pingInterval: NodeJS.Timeout | null = null;
  private reconnectTimeout: NodeJS.Timeout | null = null;
  private isConnecting = false;
  private isDestroyed = false;
  private reconnectAttempts = 0;
  private readonly maxReconnectAttempts = 10;
  private readonly reconnectDelay = 5000;
  private readonly pingIntervalMs = 30000;

  constructor(config: BitunixConfig) {
    super();
    this.config = config;
  }

  public async connect(): Promise<void> {
    if (this.isConnecting || this.ws?.readyState === WebSocket.OPEN) {
      return;
    }

    this.isConnecting = true;
    
    try {
      this.ws = new WebSocket(this.config.wsUrl);
      this.setupWebSocketHandlers();
      
      await new Promise<void>((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('WebSocket connection timeout'));
        }, 10000);

        this.ws!.once('open', () => {
          clearTimeout(timeout);
          resolve();
        });

        this.ws!.once('error', (error) => {
          clearTimeout(timeout);
          reject(error);
        });
      });

      this.isConnecting = false;
      this.reconnectAttempts = 0;
      this.startPingInterval();
      this.emit('connected');
      
      logger.info('WebSocket connected successfully');
    } catch (error) {
      this.isConnecting = false;
      logger.error('WebSocket connection failed:', error);
      this.emit('error', error as Error);
      this.scheduleReconnect();
    }
  }

  private setupWebSocketHandlers(): void {
    if (!this.ws) return;

    this.ws.on('open', () => {
      logger.info('WebSocket connection opened');
    });

    this.ws.on('message', (data: WebSocket.Data) => {
      try {
        const message = JSON.parse(data.toString());
        this.handleMessage(message);
      } catch (error) {
        logger.error('Failed to parse WebSocket message:', error);
      }
    });

    this.ws.on('close', (code: number, reason: Buffer) => {
      logger.warn('WebSocket connection closed:', { code, reason: reason.toString() });
      this.cleanup();
      this.emit('disconnected');
      
      if (!this.isDestroyed) {
        this.scheduleReconnect();
      }
    });

    this.ws.on('error', (error: Error) => {
      logger.error('WebSocket error:', error);
      this.emit('error', error);
    });
  }

  private handleMessage(message: any): void {
    try {
      if (message.op === 'ping') {
        const pongData = WebSocketPongSchema.parse(message);
        this.emit('pong', pongData);
        return;
      }

      if (message.ch === 'price') {
        const marketPriceData = MarketPriceMessageSchema.parse(message);
        this.emit('marketPrice', marketPriceData);
        return;
      }

      if (message.ch === 'trade') {
        const tradeData = TradeMessageSchema.parse(message);
        this.emit('trade', tradeData);
        return;
      }

      logger.debug('Unhandled WebSocket message:', message);
    } catch (error) {
      logger.error('Failed to handle WebSocket message:', error);
    }
  }

  private startPingInterval(): void {
    this.pingInterval = setInterval(() => {
      this.ping();
    }, this.pingIntervalMs);
  }

  private ping(): void {
    if (this.ws?.readyState === WebSocket.OPEN) {
      const pingMessage: WebSocketPing = {
        op: 'ping',
        ping: Math.floor(Date.now() / 1000),
      };
      
      this.send(pingMessage);
    }
  }

  public subscribe(symbol: string, channel: string): void {
    const subscribeMessage: WebSocketSubscribe = {
      op: 'subscribe',
      args: [{ symbol, ch: channel }],
    };
    
    this.send(subscribeMessage);
    logger.info('Subscribed to channel:', { symbol, channel });
  }

  public unsubscribe(symbol: string, channel: string): void {
    const unsubscribeMessage = {
      op: 'unsubscribe',
      args: [{ symbol, ch: channel }],
    };
    
    this.send(unsubscribeMessage);
    logger.info('Unsubscribed from channel:', { symbol, channel });
  }

  private send(message: object): void {
    if (this.ws?.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    } else {
      logger.warn('Cannot send message: WebSocket not connected');
    }
  }

  private scheduleReconnect(): void {
    if (this.isDestroyed || this.reconnectAttempts >= this.maxReconnectAttempts) {
      logger.error('Max reconnection attempts reached');
      return;
    }

    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
    
    logger.info(`Scheduling reconnection attempt ${this.reconnectAttempts} in ${delay}ms`);
    
    this.reconnectTimeout = setTimeout(() => {
      this.connect();
    }, delay);
  }

  private cleanup(): void {
    if (this.pingInterval) {
      clearInterval(this.pingInterval);
      this.pingInterval = null;
    }
    
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }
  }

  public disconnect(): void {
    this.isDestroyed = true;
    this.cleanup();
    
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    
    logger.info('WebSocket disconnected');
  }

  public isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN;
  }
}
