import fs from 'fs/promises';
import path from 'path';
import { logger } from '@/utils/logger';
import { 
  DepthData, 
  KlineData, 
  MarketPriceData, 
  TradeData,
  KlineInterval 
} from '@/types/bitunix';

export interface StoredDepthData extends DepthData {
  symbol: string;
  timestamp: number;
  limit: string;
}

export interface StoredKlineData extends KlineData {
  symbol: string;
  interval: KlineInterval;
  timestamp: number;
}

export interface StoredMarketPriceData extends MarketPriceData {
  symbol: string;
  timestamp: number;
}

export interface StoredTradeData {
  symbol: string;
  timestamp: number;
  trades: TradeData[];
}

export class DataStorage {
  private readonly dataDir: string;
  private readonly maxFileSize = 50 * 1024 * 1024; 
  private readonly maxFilesPerType = 100;

  constructor(dataDir: string = './data') {
    this.dataDir = dataDir;
    this.ensureDataDirectory();
  }

  private async ensureDataDirectory(): Promise<void> {
    try {
      await fs.mkdir(this.dataDir, { recursive: true });
      await fs.mkdir(path.join(this.dataDir, 'depth'), { recursive: true });
      await fs.mkdir(path.join(this.dataDir, 'kline'), { recursive: true });
      await fs.mkdir(path.join(this.dataDir, 'market-price'), { recursive: true });
      await fs.mkdir(path.join(this.dataDir, 'trades'), { recursive: true });
    } catch (error) {
      logger.error('Failed to create data directories:', error);
      throw error;
    }
  }

  public async storeDepthData(symbol: string, data: DepthData, limit: string): Promise<void> {
    const storedData: StoredDepthData = {
      ...data,
      symbol,
      timestamp: Date.now(),
      limit,
    };

    const filename = this.generateFilename('depth', symbol, 'json');
    await this.appendToFile(filename, storedData);
    
    logger.debug('Stored depth data:', { symbol, limit, filename });
  }

  public async hasHistoricalData(
    symbol: string,
    interval: KlineInterval,
    startTime: number
  ): Promise<boolean> {
    try {
      const filename = this.generateFilename('kline', `${symbol}_${interval}`, 'json');
      const filePath = path.join(this.dataDir, filename);

      try {
        await fs.access(filePath);
      } catch {
        return false; 
      }

      const content = await fs.readFile(filePath, 'utf-8');
      const lines = content.trim().split('\n').filter(line => line.trim());

      if (lines.length < 100) {
        return false;
      }

      for (const line of lines) {
        try {
          const data = JSON.parse(line) as StoredKlineData;
          const candleTime = parseInt(data.time);
          if (candleTime >= startTime) {
            return true; 
          }
        } catch (error) {
          continue; 
        }
      }

      return false;
    } catch (error) {
      logger.error('Failed to check historical data:', error);
      return false;
    }
  }

  public async storeKlineData(
    symbol: string,
    interval: KlineInterval,
    data: KlineData[]
  ): Promise<void> {
    const filename = this.generateFilename('kline', `${symbol}_${interval}`, 'json');

    // Store each kline as individual NDJSON line
    for (const kline of data) {
      const storedData: StoredKlineData = {
        ...kline,
        symbol,
        interval,
        timestamp: Date.now(),
      };
      await this.appendToFile(filename, storedData);
    }

    logger.debug('Stored kline data:', { symbol, interval, count: data.length, filename });
  }

  public async storeMarketPriceData(symbol: string, data: MarketPriceData): Promise<void> {
    const storedData: StoredMarketPriceData = {
      ...data,
      symbol,
      timestamp: Date.now(),
    };

    const filename = this.generateFilename('market-price', symbol, 'json');
    await this.appendToFile(filename, storedData);
    
    logger.debug('Stored market price data:', { symbol, filename });
  }

  public async storeTradeData(symbol: string, trades: TradeData[]): Promise<void> {
    const storedData: StoredTradeData = {
      symbol,
      timestamp: Date.now(),
      trades,
    };

    const filename = this.generateFilename('trades', symbol, 'json');
    await this.appendToFile(filename, storedData);
    
    logger.debug('Stored trade data:', { symbol, count: trades.length, filename });
  }

  private generateFilename(type: string, identifier: string, extension: string): string {
    return path.join(this.dataDir, type, `${identifier}.${extension}`);
  }

  private async appendToFile(filename: string, data: any): Promise<void> {
    try {
      const jsonLine = JSON.stringify(data) + '\n';
      
      try {
        const stats = await fs.stat(filename);
        if (stats.size > this.maxFileSize) {
          await this.rotateFile(filename);
        }
      } catch (error) {
      }

      await fs.appendFile(filename, jsonLine, 'utf8');
    } catch (error) {
      logger.error('Failed to append to file:', { filename, error });
      throw error;
    }
  }

  private async rotateFile(filename: string): Promise<void> {
    try {
      const timestamp = Date.now();
      const rotatedFilename = filename.replace(/\.([^.]+)$/, `_${timestamp}.$1`);
      
      await fs.rename(filename, rotatedFilename);
      logger.info('File rotated:', { original: filename, rotated: rotatedFilename });
      
      await this.cleanupOldFiles(path.dirname(filename));
    } catch (error) {
      logger.error('Failed to rotate file:', { filename, error });
    }
  }

  private async cleanupOldFiles(directory: string): Promise<void> {
    try {
      const files = await fs.readdir(directory);
      const fileStats = await Promise.all(
        files.map(async (file) => {
          const filePath = path.join(directory, file);
          const stats = await fs.stat(filePath);
          return { file: filePath, mtime: stats.mtime };
        })
      );

      fileStats.sort((a, b) => a.mtime.getTime() - b.mtime.getTime());

      if (fileStats.length > this.maxFilesPerType) {
        const filesToDelete = fileStats.slice(0, fileStats.length - this.maxFilesPerType);
        
        for (const { file } of filesToDelete) {
          await fs.unlink(file);
          logger.info('Deleted old file:', file);
        }
      }
    } catch (error) {
      logger.error('Failed to cleanup old files:', { directory, error });
    }
  }

  public async getStorageStats(): Promise<{
    totalFiles: number;
    totalSize: number;
    typeStats: Record<string, { files: number; size: number }>;
  }> {
    const types = ['depth', 'kline', 'market-price', 'trades'];
    const typeStats: Record<string, { files: number; size: number }> = {};
    let totalFiles = 0;
    let totalSize = 0;

    for (const type of types) {
      const typeDir = path.join(this.dataDir, type);
      try {
        const files = await fs.readdir(typeDir);
        let typeSize = 0;

        for (const file of files) {
          const filePath = path.join(typeDir, file);
          const stats = await fs.stat(filePath);
          typeSize += stats.size;
        }

        typeStats[type] = { files: files.length, size: typeSize };
        totalFiles += files.length;
        totalSize += typeSize;
      } catch (error) {
        typeStats[type] = { files: 0, size: 0 };
      }
    }

    return { totalFiles, totalSize, typeStats };
  }
}
