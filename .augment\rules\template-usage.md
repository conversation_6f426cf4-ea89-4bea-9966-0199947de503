---
type: "agent_requested"
description: "Example description"
---
# Template Usage Rules

### Description

AI-optimized rules for using documentation templates, maintaining consistency, and optimizing for efficient AI/LLM parsing and human navigation.

### Template Selection Decision Tree [templates](../templates/)

```
Documentation Need:
├── React/UI Component → component-template.md
├── Backend Service → service-template.md
├── Configuration/Setup → configuration-template.md
└── Custom Documentation → Adapt closest template

Template Adaptation:
├── Missing sections → Add from template
├── Extra sections → Remove if not essential
├── Wrong format → Convert to template structure
└── Outdated content → Update to current standards
```

### Template Application Protocol

| Step | Action | Validation |
| --- | --- | --- |
| 1. Select template | Match documentation type | Template relevance ✓/✗ |
| 2. Copy structure | Preserve section order | Structure compliance ✓/✗ |
| 3. Replace placeholders | Update all bracketed content | Placeholder removal ✓/✗ |
| 4. Add cross-references | Include relevant links | Reference validity ✓/✗ |
| 5. Verify standards | Check length and format | Quality standards ✓/✗ |

### Cross-Reference Standards [templates](../templates/template-usage-guide.md)

| Reference Type | Format | Usage | Validation |
| --- | --- | --- | --- |
| Source code | [src](../../path/to/file.ts) | Implementation links | File exists ✓/✗ |
| Type documentation | [→](../data-formats/type.md#interface) | Type definitions | Section exists ✓/✗ |
| API documentation | [api](../../Bitunix/endpoint.md) | External API refs | Link valid ✓/✗ |
| Related services | [ws](./websocket-client.md) | Service connections | Service exists ✓/✗ |

### Quality Validation Framework

| Standard | Requirement | Check Method | Pass/Fail |
| --- | --- | --- | --- |
| Length | 45-75 lines | Line count | ✓/✗ |
| Structure | Description → Parameters → Example → Output | Section order | ✓/✗ |
| Cross-references | Minimum 3 per file | Reference count | ✓/✗ |
| Table format | Parameter \| Type \| Required \| Description | Column structure | ✓/✗ |
| Examples | Single, practical, copy-paste ready | Example quality | ✓/✗ |

### Template Maintenance Rules

| Trigger | Action | Validation |
| --- | --- | --- |
| New documentation pattern | Update relevant template | Pattern consistency ✓/✗ |
| Quality standard change | Update all templates | Standard compliance ✓/✗ |
| Cross-reference pattern change | Update reference examples | Reference format ✓/✗ |
| User feedback | Evaluate template improvement | Usability enhancement ✓/✗ |

### AI Optimization Guidelines

| Optimization | Implementation | Benefit |
| --- | --- | --- |
| High information density | 8-10 technical facts per 10 lines | Efficient context usage |
| Predictable structure | Consistent section ordering | Fast information location |
| Table-driven specs | Parameter tables over prose | Structured data parsing |
| Minimal cross-references | Essential links only | Reduced cognitive load |
| Immediate examples | Code blocks near descriptions | Quick understanding |

### Template Conflict Resolution

| Conflict | Resolution | Priority |
| --- | --- | --- |
| Template vs existing docs | Update existing to template | Template standard |
| Template vs user preference | Clarify requirements | User needs |
| Template vs implementation | Verify implementation accuracy | Implementation truth |
| Old template vs new | Use most recent template | Latest standard |

### Documentation Creation Workflow

| Phase | Actions | Validation |
| --- | --- | --- |
| Planning | Identify documentation type, select template | Template match ✓/✗ |
| Creation | Apply template, replace placeholders | Completeness ✓/✗ |
| Content | Add technical details, examples | Accuracy ✓/✗ |
| Cross-referencing | Add relevant links | Link validity ✓/✗ |
| Review | Check standards compliance | Quality standards ✓/✗ |
| Finalization | Verify length, format, accuracy | Final validation ✓/✗ |

### Template Evolution Protocol

| Change Type | Process | Approval |
| --- | --- | --- |
| Minor improvement | Update template, document change | Self-approval |
| Major restructure | Create proposal, test with examples | User approval |
| New template | Identify need, create from scratch | User validation |
| Template removal | Deprecation notice, migration path | User confirmation |

### Error Recovery for Template Usage

| Error | Detection | Recovery |
| --- | --- | --- |
| Wrong template used | Structure mismatch | Reapply correct template |
| Missing sections | Incomplete documentation | Add missing sections |
| Broken cross-references | Link validation failure | Fix or remove references |
| Format inconsistency | Style validation failure | Reformat to template |

### Template Performance Metrics

| Metric | Target | Measurement | Action if Below Target |
| --- | --- | --- | --- |
| Documentation creation speed | <30 minutes per file | Time tracking | Simplify template |
| Template compliance rate | >95% | Validation checks | Improve template clarity |
| Cross-reference accuracy | >98% | Link validation | Update reference patterns |
| User satisfaction | >4.5/5 rating | User feedback | Template improvement |

### Context Management for Templates

| Context Scenario | Template Priority | Information Retention |
| --- | --- | --- |
| Limited context | Use most relevant template | Preserve structure, compress examples |
| Multiple templates needed | Process in dependency order | Maintain cross-references |
| Template updates required | Update most critical first | Preserve existing quality |
| User customization | Adapt template to needs | Maintain core standards |
