import { z } from 'zod';
import dotenv from 'dotenv';

dotenv.config({ path: '.env.local' });

const configSchema = z.object({
  bitunix: z.object({
    apiKey: z.string().min(1),
    secretKey: z.string().min(1),
    baseUrl: z.string().url().default('https://fapi.bitunix.com'),
    wsUrl: z.string().url().default('wss://fapi.bitunix.com/public/'),
  }),
  app: z.object({
    nodeEnv: z.enum(['development', 'production', 'test']).default('development'),
    logLevel: z.enum(['error', 'warn', 'info', 'debug']).default('info'),
    dataCollectionInterval: z.number().positive().default(60000),
    depthCollectionInterval: z.number().positive().default(1000), // 1 second for order book updates
    klineCollectionInterval: z.number().positive().default(5000), // 5 seconds for kline updates
  }),
  dataCollection: z.object({
    symbol: z.string().default('ETHUSDT'),
    klineIntervals: z.array(z.string()).default(['1m', '3m', '5m', '15m']),
    depthLimit: z.string().default('50'),
    enableWebSocket: z.boolean().default(true),
    enableRestApi: z.boolean().default(true),
    storageDir: z.string().default('./data'),
  }),
  rateLimit: z.object({
    requestsPerSecond: z.number().positive().default(10),
    burst: z.number().positive().default(5),
  }),
});

export type Config = z.infer<typeof configSchema>;

const rawConfig = {
  bitunix: {
    apiKey: process.env['BITUNIX_API_KEY'] || '',
    secretKey: process.env['BITUNIX_SECRET_KEY'] || '',
    baseUrl: process.env['BITUNIX_API_BASE_URL'],
    wsUrl: process.env['BITUNIX_WS_URL'],
  },
  app: {
    nodeEnv: process.env['NODE_ENV'],
    logLevel: process.env['LOG_LEVEL'],
    dataCollectionInterval: process.env['DATA_COLLECTION_INTERVAL']
      ? parseInt(process.env['DATA_COLLECTION_INTERVAL'], 10)
      : undefined,
    depthCollectionInterval: process.env['DEPTH_COLLECTION_INTERVAL']
      ? parseInt(process.env['DEPTH_COLLECTION_INTERVAL'], 10)
      : undefined,
    klineCollectionInterval: process.env['KLINE_COLLECTION_INTERVAL']
      ? parseInt(process.env['KLINE_COLLECTION_INTERVAL'], 10)
      : undefined,
  },
  dataCollection: {
    symbol: process.env['TRADING_SYMBOL'],
    klineIntervals: process.env['KLINE_INTERVALS']
      ? process.env['KLINE_INTERVALS'].split(',')
      : undefined,
    depthLimit: process.env['DEPTH_LIMIT'],
    enableWebSocket: process.env['ENABLE_WEBSOCKET'] === 'true',
    enableRestApi: process.env['ENABLE_REST_API'] === 'true',
    storageDir: process.env['STORAGE_DIR'],
  },
  rateLimit: {
    requestsPerSecond: process.env['RATE_LIMIT_REQUESTS_PER_SECOND']
      ? parseInt(process.env['RATE_LIMIT_REQUESTS_PER_SECOND'], 10)
      : undefined,
    burst: process.env['RATE_LIMIT_BURST']
      ? parseInt(process.env['RATE_LIMIT_BURST'], 10)
      : undefined,
  },
};

export const config = configSchema.parse(rawConfig);
