import { EventEmitter } from 'events';
import { WebSocketMessage } from '@/types/market';

export class DashboardWebSocketClient extends EventEmitter {
  private ws: WebSocket | null = null;
  private url: string;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 10;
  private reconnectDelay = 1000;
  private isDestroyed = false;
  private pingInterval: NodeJS.Timeout | null = null;

  constructor(url: string = 'ws://localhost:8080') {
    super();
    this.url = url;
  }

  public async connect(): Promise<void> {
    if (this.isDestroyed) return;

    try {
      this.ws = new WebSocket(this.url);
      this.setupEventHandlers();
      
      await new Promise<void>((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Connection timeout'));
        }, 10000);

        this.ws!.onopen = () => {
          clearTimeout(timeout);
          this.reconnectAttempts = 0;
          this.startPing();
          this.emit('connected');
          resolve();
        };

        this.ws!.onerror = (error) => {
          clearTimeout(timeout);
          reject(error);
        };
      });
    } catch (error) {
      this.emit('error', error);
      this.scheduleReconnect();
    }
  }

  private setupEventHandlers(): void {
    if (!this.ws) return;

    this.ws.onmessage = (event) => {
      try {
        const message: WebSocketMessage = JSON.parse(event.data);
        this.handleMessage(message);
      } catch (error) {
        this.emit('error', new Error('Failed to parse message'));
      }
    };

    this.ws.onclose = (event) => {
      this.cleanup();
      this.emit('disconnected', event.code, event.reason);
      
      if (!this.isDestroyed && event.code !== 1000) {
        this.scheduleReconnect();
      }
    };

    this.ws.onerror = (error) => {
      this.emit('error', error);
    };
  }

  private handleMessage(message: WebSocketMessage): void {
    switch (message.type) {
      case 'depth':
        this.emit('depth', message.data);
        break;
      case 'kline':
        this.emit('kline', message.data);
        break;
      case 'price':
        this.emit('price', message.data);
        break;
      case 'trade':
        this.emit('trade', message.data);
        break;
      case 'stats':
        this.emit('stats', message.data);
        break;
      case 'pong':
        this.emit('pong', message.data);
        break;
      case 'error':
        this.emit('error', new Error(message.data.message));
        break;
      default:
        console.warn('Unknown message type:', message.type);
    }
  }

  private startPing(): void {
    this.pingInterval = setInterval(() => {
      if (this.isConnected()) {
        this.send({
          type: 'ping',
          data: {},
          timestamp: Date.now(),
        });
      }
    }, 30000);
  }

  private cleanup(): void {
    if (this.pingInterval) {
      clearInterval(this.pingInterval);
      this.pingInterval = null;
    }
  }

  private scheduleReconnect(): void {
    if (this.isDestroyed || this.reconnectAttempts >= this.maxReconnectAttempts) {
      this.emit('error', new Error('Max reconnection attempts reached'));
      return;
    }

    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
    
    setTimeout(() => {
      if (!this.isDestroyed) {
        this.connect();
      }
    }, delay);
  }

  public send(message: WebSocketMessage): void {
    if (this.isConnected()) {
      this.ws!.send(JSON.stringify(message));
    }
  }

  public isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN;
  }

  public disconnect(): void {
    this.isDestroyed = true;
    this.cleanup();
    
    if (this.ws) {
      this.ws.close(1000, 'Client disconnect');
      this.ws = null;
    }
    
    this.removeAllListeners();
  }
}
