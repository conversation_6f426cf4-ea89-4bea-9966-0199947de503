---
type: "agent_requested"
description: "Example description"
---
# Bitunix API Integration Rules

### Description

AI-optimized rules for implementing, documenting, and maintaining Bitunix API integrations with error handling and validation protocols.

### API Implementation Priority [api](../../Docs/Bitunix/)

| Priority | Component | Validation |
| --- | --- | --- |
| 1 | Authentication [api](../../Docs/Bitunix/common/sign.md) | Signature validation ✓/✗ |
| 2 | Rate limiting [api](../../Docs/Bitunix/common/introduction.md) | 10 req/sec compliance ✓/✗ |
| 3 | Error handling | HTTP status code handling ✓/✗ |
| 4 | Data validation | Zod schema validation ✓/✗ |
| 5 | Response processing | Type-safe data transformation ✓/✗ |

### API Conflict Resolution

| Conflict | Detection | Resolution |
| --- | --- | --- |
| Docs vs API response | Schema validation failure | Test actual API, update docs if needed |
| Rate limit exceeded | 429 HTTP status | Implement exponential backoff |
| Authentication failure | 401 HTTP status | Verify credentials and signature |
| Invalid parameters | 400 HTTP status | Check parameter format against docs |

### Implementation Decision Tree

```
API Integration Request:
├── Market data → Use REST for historical, WebSocket for real-time
├── Authentication → Always use double SHA256 signature
├── Rate limiting → Implement conservative 9/10 req/sec limit
└── Error handling → Implement circuit breaker pattern

Data Collection Strategy:
├── High frequency (1s) → Order book depth
├── Medium frequency (5s) → Kline data  
├── Real-time → Trade executions via WebSocket
└── Calculated → Market statistics from collected data
```

### API Validation Framework

| Endpoint | Required Validation | Error Handling |
| --- | --- | --- |
| /depth [api](../../Docs/Bitunix/market/get-depth.md) | Symbol format, limit range | Retry with default limit |
| /kline [api](../../Docs/Bitunix/market/get-kline.md) | Interval validity, time range | Use default parameters |
| /tickers [api](../../Docs/Bitunix/market/get-tickers.md) | Symbol array format | Filter invalid symbols |
| WebSocket [api](../../Docs/Bitunix/websocket/) | Channel subscription format | Resubscribe with valid format |

### Rate Limiting Strategy

| Scenario | Action | Monitoring |
| --- | --- | --- |
| Normal operation | 9 requests/second | Track remaining quota |
| Burst needed | Use 5-request burst allowance | Monitor reset time |
| Limit exceeded | Exponential backoff (1s, 2s, 4s) | Log rate limit events |
| Sustained limit | Reduce collection frequency | Alert for manual review |

### Authentication Protocol

| Step | Implementation | Validation |
| --- | --- | --- |
| 1. Generate nonce | 32-bit random string | Uniqueness check |
| 2. Create timestamp | Current milliseconds | Time sync validation |
| 3. Build query string | URL-encoded parameters | Format validation |
| 4. Create signature | Double SHA256 [api](../../Docs/Bitunix/common/sign.md) | Signature verification |
| 5. Set headers | api-key, nonce, timestamp, sign | Header completeness |

### Error Recovery Protocols

| Error Type | Recovery Action | Escalation |
| --- | --- | --- |
| Network timeout | Retry with exponential backoff | Alert after 3 failures |
| Invalid signature | Regenerate signature | Check credential validity |
| Rate limit hit | Wait for reset + jitter | Reduce request frequency |
| Invalid symbol | Use default symbol (ETHUSDT) | Log invalid symbol usage |
| WebSocket disconnect | Reconnect with exponential backoff | Alert after 5 failures |

### Data Transformation Rules

| Input Format | Output Format | Validation |
| --- | --- | --- |
| API timestamp | Unix milliseconds | Range validation |
| Price strings | Number conversion | Numeric validation |
| Volume strings | Number conversion | Positive validation |
| Order book arrays | Sorted price levels | Sort order validation |

### Integration Testing Protocol

| Test Type | Validation | Pass Criteria |
| --- | --- | --- |
| Authentication | Successful API call | 200 HTTP status |
| Rate limiting | Sustained requests | No 429 errors |
| Data integrity | Response validation | Schema compliance |
| Error handling | Forced error conditions | Graceful recovery |
| WebSocket stability | Extended connection | No unexpected disconnects |
