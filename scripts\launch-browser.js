const { chromium } = require('playwright');

async function launchBrowser() {
  try {
    console.log('Launching browser for dashboard...');
    
    const browser = await chromium.launch({
      headless: false, // Show browser window
      args: [
        '--start-maximized',
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor'
      ]
    });
    
    const context = await browser.newContext({
      viewport: { width: 1920, height: 1080 }
    });
    
    const page = await context.newPage();
    
    // Wait for the dashboard to be ready
    console.log('Waiting for dashboard to be ready...');
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // Navigate to dashboard
    console.log('Navigating to dashboard...');
    await page.goto('http://localhost:3001', { 
      waitUntil: 'networkidle',
      timeout: 30000 
    });
    
    console.log('Dashboard loaded successfully!');
    
    // Take initial screenshot
    await page.screenshot({ 
      path: 'dashboard-screenshot.png',
      fullPage: true 
    });
    
    console.log('Screenshot saved as dashboard-screenshot.png');
    console.log('Browser is ready for inspection. Press Ctrl+C to close.');
    
    process.on('SIGINT', async () => {
      console.log('Closing browser...');
      await browser.close();
      process.exit(0);
    });
    
  } catch (error) {
    console.error('Failed to launch browser:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  launchBrowser();
}

module.exports = { launchBrowser };
