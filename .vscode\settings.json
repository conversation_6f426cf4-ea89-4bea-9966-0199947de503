{"augment.advanced": {"mcpServers": [{"name": "github", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-github"]}, {"name": "sequential-thinking", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, {"name": "memory", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"]}, {"name": "firecrawl", "command": "npx", "args": ["-y", "firecrawl-mcp"], "env": {"FIRECRAWL_API_KEY": "fc-2edfec1d86b043f88dc888d0e1836c41"}}, {"name": "context7", "command": "npx", "args": ["-y", "@upstash/context7-mcp"]}, {"name": "playwright", "command": "npx", "args": ["-y", "@playwright/mcp"]}, {"name": "markitdown", "command": "npx", "args": ["-y", "markitdown-mcp-npx"]}]}}