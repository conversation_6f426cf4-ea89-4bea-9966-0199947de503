---
type: "agent_requested"
description: "Data validation and type safety rules"
---
# Data Validation Rules

### Description

Rules for validating data integrity, type safety, and API response handling in the Bitunix Trading Application.

### Type Validation Rules [→](../../Docs/Application/data-formats/market-data.md)

| Data Type | Validation Method | Required Fields |
| --- | --- | --- |
| CandlestickData | Zod schema | x, o, h, l, c, volume |
| OrderBookData | Zod schema | bids[], asks[] |
| Trade | Zod schema | id, price, volume, side, timestamp |
| MarketStats | Zod schema | currentPrice, change24h, volume24h |

### API Response Validation

| Endpoint | Expected Format | Validation |
| --- | --- | --- |
| /depth | {code: 0, data: {bids, asks}} | Check code === 0 |
| /kline | {code: 0, data: [...]} | Validate array format |
| /tickers | {code: 0, data: [...]} | Check ticker structure |
| WebSocket | {type, data, timestamp} | Validate message type |

### Data Transformation Rules

| Input | Output | Validation |
| --- | --- | --- |
| API strings | Numbers | Parse and validate range |
| Timestamps | Unix milliseconds | Validate time range |
| Price levels | Sorted arrays | Verify sort order |
| Volume data | Positive numbers | Check > 0 |

### Error Handling Rules

| Error Type | Detection | Action |
| --- | --- | --- |
| Invalid JSON | Parse failure | Log error, skip record |
| Missing fields | Schema validation | Use default values |
| Invalid types | Type check failure | Convert or reject |
| Out of range | Range validation | Clamp or reject |

### Schema Definitions

| Schema | Purpose | Required |
| --- | --- | --- |
| CandlestickDataSchema | OHLCV validation | Yes |
| OrderBookDataSchema | Depth validation | Yes |
| TradeSchema | Trade validation | Yes |
| ConfigSchema | Configuration validation | Yes |

### Validation Performance

| Rule | Target | Action |
| --- | --- | --- |
| Validation time | <1ms per record | Optimize schema |
| Memory usage | <10MB for schemas | Reuse validators |
| Error rate | <0.1% | Improve validation |
| Throughput | >1000 records/sec | Batch validation |

### Data Integrity Rules

| Check | Method | Frequency |
| --- | --- | --- |
| Price consistency | Compare adjacent prices | Every update |
| Volume validity | Check positive values | Every record |
| Timestamp order | Verify chronological | Every batch |
| Data completeness | Check required fields | Every record |
