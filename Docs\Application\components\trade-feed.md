# Trade Feed Component

### Description [src](../../dashboard/src/components/TradeFeed.tsx)

Live trade execution feed displaying individual buy and sell transactions with real-time updates.

### Props [src](../../dashboard/src/components/TradeFeed.tsx#TradeFeedProps)

| Parameter | Type | Required | Description |
| --- | --- | --- | --- |
| trades | Trade[] [→](../data-formats/market-data.md#trade) | Yes | Array of trade executions |
| maxTrades | number | No | Maximum trades to display (default: 100) |
| showVolume | boolean | No | Show trade volume (default: true) |
| showTimestamp | boolean | No | Show execution time (default: true) |

Usage Example:

```typescript
<TradeFeed
  trades={recentTrades}
  maxTrades={100}
  showVolume={true}
  showTimestamp={true}
/>
```

### Display Format [src](../../dashboard/src/components/TradeFeed.tsx#display)

```
RECENT TRADES
Time     | Side | Price    | Volume   | Total
14:32:15 | BUY  | $2,984.50| 0.123    | $367.29
14:32:14 | SELL | $2,984.25| 0.456    | $1,361.22
14:32:13 | BUY  | $2,984.75| 0.789    | $2,355.57
14:32:12 | SELL | $2,984.00| 0.234    | $698.26
```

### Real-Time Updates [src](../../dashboard/src/components/TradeFeed.tsx#updates)

| Feature | Description |
| --- | --- |
| Live Stream | Real-time trade updates from WebSocket [→](../dashboard/websocket-server.md) |
| Auto-scroll | Automatic scrolling to latest trades |
| Highlight Animation | Flash animation for new trades |
| Performance Optimization | Virtualized scrolling for large datasets |

### Trade Visualization [src](../../dashboard/src/components/TradeFeed.tsx#visualization)

| Element | Buy Trades | Sell Trades |
| --- | --- | --- |
| Background Color | Light green | Light red |
| Text Color | Dark green | Dark red |
| Side Indicator | "BUY" | "SELL" |
| Price Trend | ↗ | ↘ |

### Trade Analysis [src](../../dashboard/src/components/TradeFeed.tsx#analysis)

| Metric | Calculation | Description |
| --- | --- | --- |
| Buy Volume | Sum of buy trade volumes | Total buying activity |
| Sell Volume | Sum of sell trade volumes | Total selling activity |
| Trade Count | Number of executions | Market activity level |
| Average Trade Size | Total Volume / Trade Count | Typical trade size |
| Volume Weighted Price | Σ(Price × Volume) / Σ(Volume) | Average execution price |

### Filtering Options [src](../../dashboard/src/components/TradeFeed.tsx#filtering)

| Filter | Options | Description |
| --- | --- | --- |
| Trade Side | All, Buy, Sell | Filter by trade direction |
| Minimum Size | Number | Filter by minimum trade volume |
| Time Range | Minutes | Show trades from last N minutes |
| Price Range | Min/Max | Filter by price range |

### Data Output [src](../../dashboard/src/components/TradeFeed.tsx#output)

| Field | Type | Description |
| --- | --- | --- |
| totalBuyVolume | number | Total volume of buy trades |
| totalSellVolume | number | Total volume of sell trades |
| tradeCount | number | Number of trades displayed |
| averageTradeSize | number | Average trade volume |
| volumeWeightedPrice | number | Volume-weighted average price |
