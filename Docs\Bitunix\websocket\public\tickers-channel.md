[Skip to content](https://openapidoc.bitunix.com/doc/websocket/public/Tickers%20Channel.html#VPContent)

Return to top

### Description [​](https://openapidoc.bitunix.com/doc/websocket/public/Tickers%20Channel.html\#description)

24-hour rolling window mini-ticker statistics for all symbols (this is aggregated stream ticker data, with a different data structure compared to individual ticker subscription). These statistics are not based on UTC daily data, but rather a 24-hour rolling window from the request time backward.

### Request Parameters [​](https://openapidoc.bitunix.com/doc/websocket/public/Tickers%20Channel.html\#request-parameters)

| Parameter | Type | Required | Description |
| --- | --- | --- | --- |
| op | String | Yes | Operation, subscribe unsubscribe |
| args | List<Object> | Yes |  |
| > symbol | String | Yes | Product ID e.g: ETHUSDT |
| > ch | String | Yes | Channel, tickers |

request example:

json

```
{
	"op": "subscribe",
	"args": [{\
			"symbol": "BTCUSDT",\
			"ch": "tickers"\
		},\
		{\
			"symbol": "ETHUSDT",\
			"ch": "tickers"\
		}\
	]
}
```

### Push Parameters [​](https://openapidoc.bitunix.com/doc/websocket/public/Tickers%20Channel.html\#push-parameters)

| Parameter | Type | Description |
| --- | --- | --- |
| ch | String | Channel name |
| ts | int64 | Time stamp |
| data | List<String> | Subscription data |
| \> s | String | symbol, Product ID E.g. ETHUSDT |
| > o | String | Opening price |
| > h | String | Highest price |
| > l | String | Lowest price |
| > la | String | Last price |
| > b | String | Trading volume of the coin |
| > q | String | Trading volume of quote currency |
| > r | String | 24-hour fluctuations |
| > bd | String | Best Bid Price |
| > ak | String | Best Ask Price |
| > bv | String | Best Bid Volume |
| > av | String | Best Ask Volume |

push data:

json

```
{
  "ch": "tickers",
  "ts": 1732178884994,
  "data":[\
    {\
        "s": "BTCUSDT",\
        "la": "0.0025",\
        "o": "0.0010",\
        "h": "0.0025",\
        "l": "0.0010",\
        "b": "10000",\
        "q": "1" ,\
        "r": "0.98",\
        "bd":"1549.41",\
        "ak":"1549.42",\
        "bv":"21.737",\
        "av":"19.853"\
   },\
   {\
        "s": "ETHUSDT",\
        "la": "0.0025",\
        "o": "0.0010",\
        "h": "0.0025",\
        "l": "0.0010",\
        "b": "10000",\
        "q": "1" ,\
        "r":"0.98",\
        "bd":"1549.41",\
        "ak":"1549.42",\
        "bv":"21.737",\
        "av":"19.853"\
   }\
  ]
}
```