[Skip to content](https://openapidoc.bitunix.com/doc/market/get_funding_rate_batch.html#VPContent)

Return to top

# Get Funding Rate [​](https://openapidoc.bitunix.com/doc/market/get_funding_rate_batch.html\#get-funding-rate)

Rate Limit: 10 req/sec/ip

### Description [​](https://openapidoc.bitunix.com/doc/market/get_funding_rate_batch.html\#description)

Get the current funding rate of the contract

### HTTP Request [​](https://openapidoc.bitunix.com/doc/market/get_funding_rate_batch.html\#http-request)

- GET /api/v1/futures/market/funding\_rate/batch

Request Example

bash

```
curl -X 'GET'  --location 'https://fapi.bitunix.com/api/v1/futures/market/funding_rate/batch'
```

### Response Parameters [​](https://openapidoc.bitunix.com/doc/market/get_funding_rate_batch.html\#response-parameters)

| Parameter | Type | Description |
| --- | --- | --- |
| symbol | string | Coin pair |
| markPrice | decimal | mark price |
| lastPrice | decimal | last price |
| fundingRate | decimal | Current funding rates |

Response Example

json

```
{"code":0,"data":[{"symbol":"BTCUSDT","markPrice":"60000","lastPrice":"60001","fundingRate":"0.0005"}],"msg":"Success"}
```