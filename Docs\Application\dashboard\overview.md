# Dashboard Overview

### Description [src](../../dashboard/src/)

Next.js frontend application providing real-time cryptocurrency trading data visualization and analysis tools.

### Architecture [src](../../dashboard/src/components/)

| Component | Type | Description |
| --- | --- | --- |
| Price Chart [→](../components/price-chart.md) | Visualization | Interactive candlestick charts |
| CVD Component [→](../components/cvd.md) | Analysis | Cumulative Volume Delta visualization |
| Order Book [→](../components/order-book.md) | Market Data | Real-time bid/ask levels |
| Trade Feed [→](../components/trade-feed.md) | Market Data | Live trade execution stream |
| Market Stats [→](../components/market-stats.md) | Statistics | Market metrics and indicators |

### Technology Stack [src](../../dashboard/package.json)

| Technology | Version | Purpose |
| --- | --- | --- |
| Next.js | 14+ | React framework with SSR |
| TypeScript | 5+ | Type-safe development |
| Chart.js | 4+ | Interactive chart visualization |
| Tailwind CSS | 3+ | Utility-first CSS framework |
| WebSocket API | Native | Real-time data connection |

### Data Flow [src](../../dashboard/src/hooks/)

```
WebSocket Server → useWebSocket Hook → Component State → UI Updates
       ↓                ↓                    ↓              ↓
   Real-time Data   State Management   React Context   Live Charts
```

### Real-Time Features [src](../../dashboard/src/hooks/useWebSocket.ts)

| Feature | Update Frequency | Description |
| --- | --- | --- |
| Price Updates | Real-time | Live price changes and market data |
| Order Book | 1 second | Bid/ask level updates |
| Trade Feed | Real-time | Individual trade executions |
| CVD Analysis | 5 seconds | Volume delta calculations |
| Chart Data | 5 seconds | Candlestick updates |

### State Management [src](../../dashboard/src/context/)

| Context | Purpose | Components |
| --- | --- | --- |
| WebSocketContext | Real-time data | All market data components |
| DashboardContext | UI state | Timeframe selection, settings |
| ThemeContext | Visual theme | Color scheme, dark/light mode |

### Development Features [src](../../dashboard/next.config.js)

| Feature | Description |
| --- | --- |
| Hot Reloading | Instant UI updates during development |
| Fast Refresh | React component state preservation |
| TypeScript Integration | Full type checking and IntelliSense |
| Tailwind JIT | Just-in-time CSS compilation |

### Performance Optimization [src](../../dashboard/src/utils/)

| Optimization | Description |
| --- | --- |
| Component Memoization | React.memo for expensive components |
| Data Throttling | Limit high-frequency updates |
| Virtual Scrolling | Efficient large list rendering |
| Chart Optimization | Canvas-based rendering for performance |

### Responsive Design [src](../../dashboard/src/styles/)

| Breakpoint | Width | Layout |
| --- | --- | --- |
| Mobile | <768px | Single column, stacked components |
| Tablet | 768px-1024px | Two column layout |
| Desktop | >1024px | Multi-column dashboard layout |
