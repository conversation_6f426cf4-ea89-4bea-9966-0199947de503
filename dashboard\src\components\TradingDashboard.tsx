'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Panel, PanelGroup, PanelResizeHandle } from 'react-resizable-panels';
import { createChart, IChartApi, ISeriesApi, CandlestickData, LineData } from 'lightweight-charts';
import { useQuery } from '@tanstack/react-query';
import { TimeFrame } from '@/types/market';

interface TradingDashboardProps {
  symbol: string;
  initialTimeframe?: TimeFrame;
}

interface ChartData {
  price: CandlestickData[];
  volume: LineData[];
  cvd: LineData[];
}

export default function TradingDashboard({ 
  symbol = 'ETHUSDT', 
  initialTimeframe = '1m' 
}: TradingDashboardProps) {
  const [timeframe, setTimeframe] = useState<TimeFrame>(initialTimeframe);
  const [isLoading, setIsLoading] = useState(true);
  
  // Chart refs
  const priceChartRef = useRef<HTMLDivElement>(null);
  const volumeChartRef = useRef<HTMLDivElement>(null);
  const cvdChartRef = useRef<HTMLDivElement>(null);
  
  // Chart instances
  const priceChart = useRef<IChartApi | null>(null);
  const volumeChart = useRef<IChartApi | null>(null);
  const cvdChart = useRef<IChartApi | null>(null);
  
  // Series refs
  const candlestickSeries = useRef<ISeriesApi<'Candlestick'> | null>(null);
  const volumeSeries = useRef<ISeriesApi<'Line'> | null>(null);
  const cvdSeries = useRef<ISeriesApi<'Line'> | null>(null);

  // Fetch market data using React Query
  const { data: chartData, isError } = useQuery({
    queryKey: ['chartData', symbol, timeframe],
    queryFn: async (): Promise<ChartData> => {
      const response = await fetch(`/api/market-data?symbol=${symbol}&timeframe=${timeframe}`);
      if (!response.ok) throw new Error('Failed to fetch market data');
      return response.json();
    },
    refetchInterval: 5000, // Refetch every 5 seconds
    staleTime: 1000, // Consider data stale after 1 second
  });

  // Initialize charts
  useEffect(() => {
    if (!priceChartRef.current || !volumeChartRef.current || !cvdChartRef.current) return;

    // Price Chart
    priceChart.current = createChart(priceChartRef.current, {
      width: priceChartRef.current.clientWidth,
      height: 400,
      layout: {
        background: { color: '#ffffff' },
        textColor: '#333',
      },
      grid: {
        vertLines: { color: '#f0f0f0' },
        horzLines: { color: '#f0f0f0' },
      },
      crosshair: {
        mode: 1, // Normal crosshair
      },
      rightPriceScale: {
        borderColor: '#cccccc',
      },
      timeScale: {
        borderColor: '#cccccc',
        timeVisible: true,
        secondsVisible: false,
      },
    });

    candlestickSeries.current = priceChart.current.addCandlestickSeries({
      upColor: '#26a69a',
      downColor: '#ef5350',
      borderVisible: false,
      wickUpColor: '#26a69a',
      wickDownColor: '#ef5350',
    });

    // Volume Chart
    volumeChart.current = createChart(volumeChartRef.current, {
      width: volumeChartRef.current.clientWidth,
      height: 150,
      layout: {
        background: { color: '#ffffff' },
        textColor: '#333',
      },
      grid: {
        vertLines: { color: '#f0f0f0' },
        horzLines: { color: '#f0f0f0' },
      },
      rightPriceScale: {
        borderColor: '#cccccc',
      },
      timeScale: {
        borderColor: '#cccccc',
        visible: false,
      },
    });

    volumeSeries.current = volumeChart.current.addLineSeries({
      color: '#2196F3',
      lineWidth: 2,
    });

    // CVD Chart
    cvdChart.current = createChart(cvdChartRef.current, {
      width: cvdChartRef.current.clientWidth,
      height: 150,
      layout: {
        background: { color: '#ffffff' },
        textColor: '#333',
      },
      grid: {
        vertLines: { color: '#f0f0f0' },
        horzLines: { color: '#f0f0f0' },
      },
      rightPriceScale: {
        borderColor: '#cccccc',
      },
      timeScale: {
        borderColor: '#cccccc',
        visible: false,
      },
    });

    cvdSeries.current = cvdChart.current.addLineSeries({
      color: '#FF6B35',
      lineWidth: 2,
    });

    // Sync time scales
    const syncCharts = () => {
      if (priceChart.current && volumeChart.current && cvdChart.current) {
        const timeScale = priceChart.current.timeScale();
        volumeChart.current.timeScale().subscribeVisibleTimeRangeChange(() => {
          const visibleRange = volumeChart.current?.timeScale().getVisibleRange();
          if (visibleRange && cvdChart.current) {
            timeScale.setVisibleRange(visibleRange);
            cvdChart.current.timeScale().setVisibleRange(visibleRange);
          }
        });
      }
    };

    syncCharts();
    setIsLoading(false);

    // Cleanup
    return () => {
      priceChart.current?.remove();
      volumeChart.current?.remove();
      cvdChart.current?.remove();
    };
  }, []);

  // Update chart data
  useEffect(() => {
    if (!chartData || !candlestickSeries.current || !volumeSeries.current || !cvdSeries.current) return;

    candlestickSeries.current.setData(chartData.price);
    volumeSeries.current.setData(chartData.volume);
    cvdSeries.current.setData(chartData.cvd);
  }, [chartData]);

  // Handle resize
  useEffect(() => {
    const handleResize = () => {
      if (priceChart.current && priceChartRef.current) {
        priceChart.current.applyOptions({ width: priceChartRef.current.clientWidth });
      }
      if (volumeChart.current && volumeChartRef.current) {
        volumeChart.current.applyOptions({ width: volumeChartRef.current.clientWidth });
      }
      if (cvdChart.current && cvdChartRef.current) {
        cvdChart.current.applyOptions({ width: cvdChartRef.current.clientWidth });
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const timeframes: { value: TimeFrame; label: string }[] = [
    { value: '1m', label: '1M' },
    { value: '3m', label: '3M' },
    { value: '5m', label: '5M' },
    { value: '15m', label: '15M' },
  ];

  if (isError) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-red-600 mb-2">Error Loading Dashboard</h2>
          <p className="text-gray-600">Failed to load market data. Please check your connection.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200 px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <h1 className="text-xl font-bold text-gray-900">Bitunix Trading Dashboard</h1>
            <span className="text-lg font-semibold text-blue-600">{symbol}</span>
          </div>
          
          {/* Timeframe Selector */}
          <div className="flex space-x-1 bg-gray-100 rounded-lg p-1">
            {timeframes.map(({ value, label }) => (
              <button
                key={value}
                onClick={() => setTimeframe(value)}
                className={`px-3 py-1 text-sm font-medium rounded-md transition-colors ${
                  timeframe === value
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                {label}
              </button>
            ))}
          </div>
        </div>
      </header>

      {/* Main Content */}
      <div className="h-[calc(100vh-64px)]">
        <PanelGroup direction="horizontal">
          {/* Charts Panel */}
          <Panel defaultSize={75} minSize={60}>
            <div className="h-full bg-white border-r border-gray-200">
              <PanelGroup direction="vertical">
                {/* Price Chart */}
                <Panel defaultSize={60} minSize={40}>
                  <div className="h-full p-4">
                    <div className="h-full border border-gray-200 rounded-lg">
                      <div ref={priceChartRef} className="w-full h-full" />
                    </div>
                  </div>
                </Panel>
                
                <PanelResizeHandle className="h-2 bg-gray-100 hover:bg-gray-200 transition-colors" />
                
                {/* Volume Chart */}
                <Panel defaultSize={20} minSize={15}>
                  <div className="h-full p-4">
                    <div className="h-full border border-gray-200 rounded-lg">
                      <div ref={volumeChartRef} className="w-full h-full" />
                    </div>
                  </div>
                </Panel>
                
                <PanelResizeHandle className="h-2 bg-gray-100 hover:bg-gray-200 transition-colors" />
                
                {/* CVD Chart */}
                <Panel defaultSize={20} minSize={15}>
                  <div className="h-full p-4">
                    <div className="h-full border border-gray-200 rounded-lg">
                      <div ref={cvdChartRef} className="w-full h-full" />
                    </div>
                  </div>
                </Panel>
              </PanelGroup>
            </div>
          </Panel>
          
          <PanelResizeHandle className="w-2 bg-gray-100 hover:bg-gray-200 transition-colors" />
          
          {/* Side Panel */}
          <Panel defaultSize={25} minSize={20}>
            <div className="h-full bg-white p-4">
              <h3 className="text-lg font-semibold mb-4">Market Data</h3>
              {isLoading ? (
                <div className="animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                </div>
              ) : (
                <div className="space-y-4">
                  <div>
                    <p className="text-sm text-gray-600">Status</p>
                    <p className="font-semibold text-green-600">Connected</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Timeframe</p>
                    <p className="font-semibold">{timeframe}</p>
                  </div>
                </div>
              )}
            </div>
          </Panel>
        </PanelGroup>
      </div>
    </div>
  );
}
