# Price Chart Component

### Description [src](../../dashboard/src/components/PriceChart.tsx)

Interactive candlestick chart component with multiple timeframes and real-time updates.

### Props [src](../../dashboard/src/components/PriceChart.tsx#PriceChartProps)

| Parameter | Type | Required | Description |
| --- | --- | --- | --- |
| data | CandlestickData[] [→](../data-formats/market-data.md#candlestickdata) | Yes | OHLCV candlestick data |
| timeframe | TimeFrame [→](../data-formats/market-data.md#timeframe) | Yes | Selected timeframe ('1m', '3m', '5m', '15m') |
| onTimeframeChange | (timeframe: TimeFrame) => void | Yes | Timeframe selection callback |
| height | number | No | Chart height in pixels (default: 400) |

Usage Example:

```typescript
<PriceChart
  data={candlestickData}
  timeframe={selectedTimeframe}
  onTimeframeChange={setSelectedTimeframe}
  height={400}
/>
```

### Chart Configuration [src](../../dashboard/src/components/PriceChart.tsx#chartConfig)

```typescript
const chartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  scales: {
    x: { type: 'time', time: { unit: 'minute' } },
    y: { position: 'right', grid: { color: 'rgba(0,0,0,0.1)' } },
  },
  plugins: {
    legend: { display: false },
    tooltip: { mode: 'index', intersect: false },
  },
};
```

### Timeframe Controls [src](../../dashboard/src/components/PriceChart.tsx#timeframeControls)

| Timeframe | Interval | Data Points | Description |
| --- | --- | --- | --- |
| 1m | 1 minute | 200 | High-frequency short-term analysis |
| 3m | 3 minutes | 200 | Medium-frequency analysis |
| 5m | 5 minutes | 200 | Standard trading timeframe |
| 15m | 15 minutes | 200 | Longer-term trend analysis |

### Real-Time Updates [src](../../dashboard/src/components/PriceChart.tsx#updates)

| Feature | Description |
| --- | --- |
| Live Data | Automatic updates from WebSocket server [→](../dashboard/websocket-server.md) |
| Smooth Animation | Chart.js animations for data transitions |
| Performance Optimization | Efficient rendering for high-frequency updates |
| Memory Management | Automatic data point limiting |

### Chart Features [src](../../dashboard/src/components/PriceChart.tsx#features)

| Feature | Description |
| --- | --- |
| Candlestick Visualization | OHLC data with volume |
| Zoom and Pan | Interactive chart navigation |
| Crosshair Cursor | Precise data point inspection |
| Responsive Design | Adapts to container size |
| Touch Support | Mobile-friendly interactions |

### Data Output [src](../../dashboard/src/components/PriceChart.tsx#output)

| Event | Data | Description |
| --- | --- | --- |
| onTimeframeChange | TimeFrame | Timeframe selection change |
| onDataUpdate | CandlestickData[] | Chart data updates |
| onChartReady | Chart instance | Chart initialization complete |
