# Configuration Management

### Description [src](../../src/config/config.ts)

Environment-based configuration with support for development, production, and custom environments.

### Configuration Parameters [src](../../src/config/config.ts#Config)

| Parameter | Type | Required | Description |
| --- | --- | --- | --- |
| BITUNIX_API_KEY | string | Yes | Bitunix API key [api](../Bitunix/common/introduction.md) |
| BITUNIX_SECRET_KEY | string | Yes | Bitunix secret key [api](../Bitunix/common/introduction.md) |
| TRADING_SYMBOL | string | No | Trading pair (default: ETHUSDT) |
| KLINE_INTERVALS | string | No | Timeframes (default: 1m,3m,5m,15m) |
| DEPTH_LIMIT | string | No | Order book depth (default: 50) [api](../Bitunix/market/get-depth.md) |
| DASHBOARD_PORT | number | No | Dashboard port (default: 3001) |
| WEBSOCKET_PORT | number | No | WebSocket port (default: 8080) |

### Environment Variables

| Variable | Type | Required | Description |
| --- | --- | --- | --- |
| NODE_ENV | string | No | Environment (development/production) |
| LOG_LEVEL | string | No | Logging level (default: info) |
| DEPTH_COLLECTION_INTERVAL | number | No | Order book interval (default: 1000ms) |
| KLINE_COLLECTION_INTERVAL | number | No | Kline interval (default: 5000ms) |
Configuration Example:

```env
# .env.local
BITUNIX_API_KEY=your_api_key_here
BITUNIX_SECRET_KEY=your_secret_key_here
TRADING_SYMBOL=ETHUSDT
KLINE_INTERVALS=1m,3m,5m,15m
DEPTH_LIMIT=50
DASHBOARD_PORT=3001
WEBSOCKET_PORT=8080
NODE_ENV=development
LOG_LEVEL=info
DEPTH_COLLECTION_INTERVAL=1000
KLINE_COLLECTION_INTERVAL=5000
```

### Configuration Hierarchy

| Priority | Source | Description |
| --- | --- | --- |
| 1 | Environment variables | Highest priority |
| 2 | .env.local file | Local development |
| 3 | .env file | Default values |
| 4 | Code defaults | Lowest priority |

### Validation [src](../../src/config/validation.ts)

| Rule | Description |
| --- | --- |
| Required fields | API credentials must be provided |
| Type validation | Parameters must match specified types |
| Range validation | Intervals must be positive numbers |
| URL validation | API URLs must be valid HTTPS/WSS |

### Security Considerations

| Consideration | Description |
| --- | --- |
| API Keys | Store in .env.local, never commit to code [api](../Bitunix/common/introduction.md) |
| File Permissions | chmod 600 .env.local |
| Production | Use environment variables, not files |
| Rotation | Rotate API keys regularly |
