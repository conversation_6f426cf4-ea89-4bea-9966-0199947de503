# Bitunix Trading Application Documentation

### Description

Complete documentation for the Bitunix Trading Application and Bitunix API integration. Optimized for AI/LLM parsing with standardized structure and cross-references.

### Documentation Structure

| Category | Files | Description |
| --- | --- | --- |
| **Bitunix API** | 11 files | Official exchange API documentation |
| **Application** | 20 files | Custom trading application documentation |
| **Templates** | 4 files | Documentation templates and standards |

### 🔌 Bitunix API Documentation

| Category | Files | Description |
| --- | --- | --- |
| Core API | [Introduction](./Bitunix/common/introduction.md), [Signature](./Bitunix/common/sign.md) | Authentication and signing |
| Market Data | [Tickers](./Bitunix/market/get-tickers.md), [Depth](./Bitunix/market/get-depth.md), [Kline](./Bitunix/market/get-kline.md) | REST endpoints |
| WebSocket | [Setup](./Bitunix/websocket/prepare/websocket.md), [Channels](./Bitunix/websocket/public/) | Real-time data streams |

### 🚀 Application Documentation

| Category | Files | Description |
| --- | --- | --- |
| Getting Started | [Overview](./Application/README.md), [Introduction](./Application/introduction.md), [Installation](./Application/installation.md) | Setup and architecture |
| Core Services | [Data Collector](./Application/services/data-collector.md), [API Client](./Application/services/api-client.md), [WebSocket Client](./Application/services/websocket-client.md), [Storage](./Application/services/storage.md) | Backend services |
| Dashboard | [Overview](./Application/dashboard/overview.md), [WebSocket Server](./Application/dashboard/websocket-server.md), [Data Processor](./Application/dashboard/data-processor.md) | Frontend system |
| Components | [CVD](./Application/components/cvd.md), [Price Chart](./Application/components/price-chart.md), [Order Book](./Application/components/order-book.md), [Trade Feed](./Application/components/trade-feed.md) | UI components |
| Development | [Scripts](./Application/development/scripts.md), [Configuration](./Application/configuration.md) | Development workflow |
| Data Formats | [Market Data](./Application/data-formats/market-data.md), [Storage](./Application/data-formats/storage.md), [WebSocket](./Application/data-formats/websocket.md) | Data structures |
### Quick Navigation

| For | Start Here | Then |
| --- | --- | --- |
| New Developers | [Introduction](./Application/introduction.md) | [Installation](./Application/installation.md) → [Scripts](./Application/development/scripts.md) |
| System Integration | [Data Collector](./Application/services/data-collector.md) | [Market Data Types](./Application/data-formats/market-data.md) |
| Frontend Development | [Dashboard Overview](./Application/dashboard/overview.md) | [Components](./Application/components/) |
| Operations | [Configuration](./Application/configuration.md) | [Scripts Guide](./Application/development/scripts.md) |

### Architecture Overview

```
Bitunix API → Data Collector → Storage → WebSocket Server → Dashboard
     ↓              ↓            ↓           ↓              ↓
  REST/WS      Validation    JSON Files   Real-time     Visualization
```

### Key Features

| Feature | Component | Description |
| --- | --- | --- |
| High-Frequency Collection | [Data Collector](./Application/services/data-collector.md) | 1s order book, 5s kline updates |
| Real-Time Visualization | [CVD Component](./Application/components/cvd.md) | Cumulative Volume Delta analysis |
| WebSocket Distribution | [WebSocket Server](./Application/dashboard/websocket-server.md) | Live data streaming |
| Multiple Timeframes | [Market Data Types](./Application/data-formats/market-data.md) | 1m, 3m, 5m, 15m synchronization |

### Service URLs

| Service | URL | Documentation |
| --- | --- | --- |
| Dashboard | http://localhost:3001 | [Dashboard Overview](./Application/dashboard/overview.md) |
| WebSocket Server | ws://localhost:8080 | [WebSocket Server](./Application/dashboard/websocket-server.md) |
| Data Storage | ./data/ | [Storage System](./Application/services/storage.md) |

### Documentation Standards

This documentation follows Bitunix API standards:
- **Length**: 45-75 lines per file (optimized for AI parsing)
- **Structure**: Description → Parameters → Example → Output
- **Cross-references**: Enhanced navigation with minimal overhead
- **Technical focus**: Specification over explanation

### Templates and Standards

| Template | Purpose | Location |
| --- | --- | --- |
| Component Template | React/UI components | [.augment/templates/component-template.md](../.augment/templates/component-template.md) |
| Service Template | Backend services | [.augment/templates/service-template.md](../.augment/templates/service-template.md) |
| Configuration Template | Setup and config | [.augment/templates/configuration-template.md](../.augment/templates/configuration-template.md) |
| Usage Guide | Template application | [.augment/templates/template-usage-guide.md](../.augment/templates/template-usage-guide.md) |

### Quick Start

```bash
# Setup
npm run app:install
cp .env.example .env.local
# Edit .env.local with Bitunix API credentials

# Development
npm run app:dev
```

**Complete knowledge base for the Bitunix Trading Application - optimized for AI/LLM parsing and human navigation.**
