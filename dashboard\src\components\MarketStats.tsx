'use client'

import React, { useState, useEffect } from 'react'
import { MarketStats as MarketStatsType } from '@/types/market'
import { format } from 'date-fns'
import { TrendingUp, TrendingDown, Activity, Clock, DollarSign, BarChart3 } from 'lucide-react'

interface MarketStatsProps {
  stats: MarketStatsType | null
  isLoading?: boolean
}

interface StatCardProps {
  title: string
  value: string
  change?: string
  changeType?: 'positive' | 'negative' | 'neutral'
  icon: React.ReactNode
  subtitle?: string
  isFlashing?: boolean
}

function StatCard({ 
  title, 
  value, 
  change, 
  changeType = 'neutral', 
  icon, 
  subtitle,
  isFlashing = false 
}: StatCardProps) {
  const changeColors = {
    positive: 'text-success-600 bg-success-50',
    negative: 'text-danger-600 bg-danger-50',
    neutral: 'text-gray-600 bg-gray-50',
  }

  return (
    <div className={`metric-card transition-all duration-300 ${isFlashing ? 'flash-green' : ''}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-primary-50 rounded-lg">
            {icon}
          </div>
          <div>
            <p className="metric-label">{title}</p>
            <p className="metric-value">{value}</p>
            {subtitle && (
              <p className="text-xs text-gray-500 mt-1">{subtitle}</p>
            )}
          </div>
        </div>
        {change && (
          <div className={`px-2 py-1 rounded-full text-xs font-medium ${changeColors[changeType]}`}>
            {changeType === 'positive' && <TrendingUp className="inline h-3 w-3 mr-1" />}
            {changeType === 'negative' && <TrendingDown className="inline h-3 w-3 mr-1" />}
            {change}
          </div>
        )}
      </div>
    </div>
  )
}

export default function MarketStats({ stats, isLoading = false }: MarketStatsProps) {
  const [isClient, setIsClient] = useState(false)
  const [prevStats, setPrevStats] = useState<MarketStatsType | null>(null)
  const [flashingStats, setFlashingStats] = useState<Set<string>>(new Set())

  useEffect(() => {
    setIsClient(true)
  }, [])

  useEffect(() => {
    if (stats && prevStats) {
      const newFlashing = new Set<string>()
      
      if (stats.currentPrice !== prevStats.currentPrice) {
        newFlashing.add('price')
      }
      if (stats.volume24h !== prevStats.volume24h) {
        newFlashing.add('volume')
      }
      if (stats.high24h !== prevStats.high24h) {
        newFlashing.add('high')
      }
      if (stats.low24h !== prevStats.low24h) {
        newFlashing.add('low')
      }
      
      setFlashingStats(newFlashing)
            setTimeout(() => setFlashingStats(new Set()), 500)
    }
    
    setPrevStats(stats)
  }, [stats, prevStats])

  if (!stats) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
        {Array.from({ length: 6 }).map((_, index) => (
          <div key={index} className="metric-card animate-pulse">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gray-200 rounded-lg"></div>
              <div>
                <div className="h-3 bg-gray-200 rounded w-16 mb-2"></div>
                <div className="h-6 bg-gray-200 rounded w-20"></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    )
  }

  const formatPrice = (price: number) => `$${price.toFixed(2)}`
  const formatVolume = (volume: number) => {
    if (volume >= 1000000) {
      return `${(volume / 1000000).toFixed(2)}M`
    } else if (volume >= 1000) {
      return `${(volume / 1000).toFixed(2)}K`
    }
    return volume.toFixed(2)
  }

  const changeType = stats.change24h > 0 ? 'positive' : stats.change24h < 0 ? 'negative' : 'neutral'
  const changeText = `${stats.change24h >= 0 ? '+' : ''}${stats.change24h.toFixed(2)} (${stats.changePercent24h >= 0 ? '+' : ''}${stats.changePercent24h.toFixed(2)}%)`

  return (
    <div className="space-y-4">
      {isLoading && (
        <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
        <StatCard
          title="Current Price"
          value={formatPrice(stats.currentPrice)}
          change={changeText}
          changeType={changeType}
          icon={<DollarSign className="h-5 w-5 text-primary-600" />}
          isFlashing={flashingStats.has('price')}
        />

        <StatCard
          title="24h High"
          value={formatPrice(stats.high24h)}
          icon={<TrendingUp className="h-5 w-5 text-success-600" />}
          isFlashing={flashingStats.has('high')}
        />

        <StatCard
          title="24h Low"
          value={formatPrice(stats.low24h)}
          icon={<TrendingDown className="h-5 w-5 text-danger-600" />}
          isFlashing={flashingStats.has('low')}
        />

        <StatCard
          title="24h Volume"
          value={`${formatVolume(stats.volume24h)} ETH`}
          subtitle={`$${formatVolume(stats.volume24h * stats.currentPrice)}`}
          icon={<BarChart3 className="h-5 w-5 text-primary-600" />}
          isFlashing={flashingStats.has('volume')}
        />

        <StatCard
          title="Spread"
          value={`$${stats.spread.toFixed(2)}`}
          subtitle={`${((stats.spread / stats.currentPrice) * 100).toFixed(3)}%`}
          icon={<Activity className="h-5 w-5 text-primary-600" />}
        />

        <StatCard
          title="Last Update"
          value={isClient ? format(new Date(stats.lastUpdate), 'HH:mm:ss') : '--:--:--'}
          subtitle={isClient ? format(new Date(stats.lastUpdate), 'MMM dd') : '---'}
          icon={<Clock className="h-5 w-5 text-primary-600" />}
        />
      </div>

      {/* Additional market info */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="metric-card">
          <div className="text-center">
            <p className="metric-label">Price Range (24h)</p>
            <div className="flex items-center justify-between mt-2">
              <span className="text-sm text-danger-600 font-medium">
                ${stats.low24h.toFixed(2)}
              </span>
              <div className="flex-1 mx-3 h-2 bg-gray-200 rounded-full overflow-hidden">
                <div 
                  className="h-full bg-gradient-to-r from-danger-500 via-warning-500 to-success-500"
                  style={{
                    width: `${((stats.currentPrice - stats.low24h) / (stats.high24h - stats.low24h)) * 100}%`
                  }}
                />
              </div>
              <span className="text-sm text-success-600 font-medium">
                ${stats.high24h.toFixed(2)}
              </span>
            </div>
          </div>
        </div>

        <div className="metric-card">
          <div className="text-center">
            <p className="metric-label">Market Sentiment</p>
            <div className="mt-2">
              {stats.changePercent24h > 2 ? (
                <div className="flex items-center justify-center text-success-600">
                  <TrendingUp className="h-5 w-5 mr-2" />
                  <span className="font-medium">Bullish</span>
                </div>
              ) : stats.changePercent24h < -2 ? (
                <div className="flex items-center justify-center text-danger-600">
                  <TrendingDown className="h-5 w-5 mr-2" />
                  <span className="font-medium">Bearish</span>
                </div>
              ) : (
                <div className="flex items-center justify-center text-gray-600">
                  <Activity className="h-5 w-5 mr-2" />
                  <span className="font-medium">Neutral</span>
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="metric-card">
          <div className="text-center">
            <p className="metric-label">Trading Pair</p>
            <div className="mt-2">
              <div className="text-2xl font-bold text-gray-900">ETH/USDT</div>
              <div className="text-sm text-gray-500">Ethereum / Tether USD</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
