# Storage Formats

### Description [src](../../src/services/DataStorage.ts)

File storage structures and formats used for persistent market data storage in JSON Lines format.

### File Structure [src](../../src/services/DataStorage.ts#fileStructure)

```
data/
├── depth/
│   └── ETHUSDT_depth_50_20231201.jsonl.gz
├── kline/
│   ├── ETHUSDT_kline_1m_20231201.jsonl.gz
│   ├── ETHUSDT_kline_3m_20231201.jsonl.gz
│   └── ETHUSDT_kline_5m_20231201.jsonl.gz
├── trades/
│   └── ETHUSDT_trades_20231201.jsonl.gz
└── market-price/
    └── ETHUSDT_price_20231201.jsonl.gz
```

### Depth Data Format [src](../../src/services/DataStorage.ts#depthFormat)

```json
{
  "symbol": "ETHUSDT",
  "timestamp": 1703123456789,
  "limit": "50",
  "bids": [
    ["2984.50", "1.234"],
    ["2984.25", "0.567"]
  ],
  "asks": [
    ["2984.75", "0.890"],
    ["2985.00", "1.456"]
  ]
}
```

### Kline Data Format [src](../../src/services/DataStorage.ts#klineFormat)

```json
{
  "symbol": "ETHUSDT",
  "interval": "1m",
  "timestamp": 1703123456789,
  "time": "2023-12-01T10:30:00.000Z",
  "open": "2984.25",
  "high": "2985.50",
  "low": "2983.75",
  "close": "2984.80",
  "baseVol": "12.345"
}
```

### Trade Data Format [src](../../src/services/DataStorage.ts#tradeFormat)

```json
{
  "symbol": "ETHUSDT",
  "timestamp": 1703123456789,
  "trades": [
    {
      "t": "1703123456789",
      "p": "2984.50",
      "v": "0.123",
      "s": "buy"
    }
  ]
}
```

### Market Price Format [src](../../src/services/DataStorage.ts#priceFormat)

```json
{
  "symbol": "ETHUSDT",
  "timestamp": 1703123456789,
  "price": "2984.50",
  "change24h": "45.25",
  "changePercent24h": "1.54",
  "volume24h": "1234.567"
}
```

### File Naming Convention [src](../../src/services/DataStorage.ts#naming)

| Pattern | Example | Description |
| --- | --- | --- |
| Depth | SYMBOL_depth_LIMIT_YYYYMMDD.jsonl.gz | Order book depth files |
| Kline | SYMBOL_kline_INTERVAL_YYYYMMDD.jsonl.gz | Candlestick data files |
| Trades | SYMBOL_trades_YYYYMMDD.jsonl.gz | Trade execution files |
| Price | SYMBOL_price_YYYYMMDD.jsonl.gz | Market price files |

### Compression [src](../../src/services/DataStorage.ts#compression)

| Feature | Description |
| --- | --- |
| Format | Gzip compression (.gz) |
| Ratio | ~70% size reduction |
| Performance | Minimal CPU overhead |
| Compatibility | Standard gzip format |

### File Rotation [src](../../src/services/DataStorage.ts#rotation)

| Trigger | Action | Description |
| --- | --- | --- |
| File size > 50MB | Create new file | Automatic file rotation |
| Date change | Create new file | Daily file separation |
| Service restart | Continue existing | Resume writing to current file |
