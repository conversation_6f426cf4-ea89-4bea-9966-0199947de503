# Application Introduction

### Description [src](../../src/index.ts)

Real-time cryptocurrency data collection and visualization system for professional trading analysis.

### System Components [src](../../src/services/)

| Component | Type | Description |
| --- | --- | --- |
| BitunixDataCollector [→](./services/data-collector.md) | Service | Main data collection orchestrator |
| BitunixApiClient [→](./services/api-client.md) | Service | REST API client with authentication |
| BitunixWebSocketClient [→](./services/websocket-client.md) | Service | Real-time data streaming |
| WebSocket Server [→](./dashboard/websocket-server.md) | Service | Real-time data distribution |
| Dashboard [→](./dashboard/overview.md) | Frontend | Next.js visualization interface |

### Data Flow

```
Bitunix API → Data Collector → Storage → WebSocket Server → Dashboard
     ↓              ↓            ↓           ↓              ↓
  REST/WS      Validation    JSON Files   Real-time     Visualization
```

### Performance Characteristics

| Metric | Value | Description |
| --- | --- | --- |
| API Utilization | 75 req/min | 12.5% of 600 req/min limit [api](../Bitunix/common/introduction.md) |
| Update Frequency | 1s/5s | Order book (1s), Klines (5s) |
| Data Growth | ~1MB/min | During active trading |
| Storage Format | JSON Lines | Automatic rotation and compression |

### Key Features

| Feature | Description |
| --- | --- |
| CVD Analysis [→](./components/cvd.md) | Cumulative Volume Delta visualization |
| Order Book [→](./components/order-book.md) | Live bid/ask levels with spread analysis |
| Trade Feed [→](./components/trade-feed.md) | Individual trade executions |
| Multiple Timeframes | 1m, 3m, 5m, 15m synchronized |

### Quick Start

```bash
# Installation
npm run app:install

# Configuration
cp .env.example .env.local
# Edit .env.local with Bitunix API credentials

# Launch
npm run app:dev
```

### Service URLs

| Service | URL | Description |
| --- | --- | --- |
| Dashboard | http://localhost:3001 | Main visualization interface |
| WebSocket Server | ws://localhost:8080 | Real-time data distribution |
| Data Storage | ./data/ | JSON Lines data files |

For detailed setup: [Configuration Guide](./configuration.md) • [Scripts Guide](./development/scripts.md)
