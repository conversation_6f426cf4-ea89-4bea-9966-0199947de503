@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: system-ui, sans-serif;
  }
  
  body {
    @apply bg-gray-50 text-gray-900;
  }
}

@layer components {
  .card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
  }
  
  .card-header {
    @apply flex items-center justify-between mb-4 pb-2 border-b border-gray-100;
  }
  
  .card-title {
    @apply text-lg font-semibold text-gray-900;
  }
  
  .metric-card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200 p-4;
  }
  
  .metric-label {
    @apply text-sm font-medium text-gray-500 uppercase tracking-wide;
  }
  
  .metric-value {
    @apply text-2xl font-bold text-gray-900 mt-1;
  }
  
  .status-indicator {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .status-connected {
    @apply bg-green-100 text-green-800;
  }
  
  .status-disconnected {
    @apply bg-red-100 text-red-800;
  }
  
  .status-connecting {
    @apply bg-yellow-100 text-yellow-800;
  }
  
  .btn {
    @apply inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2;
  }
  
  .btn-primary {
    @apply text-white bg-primary-600 hover:bg-primary-700 focus:ring-primary-500;
  }
  
  .btn-secondary {
    @apply text-gray-700 bg-white hover:bg-gray-50 border-gray-300 focus:ring-primary-500;
  }
  
  .btn-success {
    @apply text-white bg-success-600 hover:bg-success-700 focus:ring-success-500;
  }
  
  .btn-danger {
    @apply text-white bg-danger-600 hover:bg-danger-700 focus:ring-danger-500;
  }
  
  .trade-buy {
    @apply text-success-600;
  }
  
  .trade-sell {
    @apply text-danger-600;
  }
  
  .price-up {
    @apply text-success-600 bg-success-50;
  }
  
  .price-down {
    @apply text-danger-600 bg-danger-50;
  }
  
  .price-neutral {
    @apply text-gray-600 bg-gray-50;
  }
}

@layer utilities {
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgb(156 163 175) transparent;
  }
  
  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: rgb(156 163 175);
    border-radius: 3px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: rgb(107 114 128);
  }
}

/* Chart container styles */
.chart-container {
  position: relative;
  height: 400px;
  width: 100%;
}

.chart-container canvas {
  max-height: 400px !important;
}

/* Animation for real-time updates */
@keyframes flash {
  0% { background-color: rgba(34, 197, 94, 0.2); }
  100% { background-color: transparent; }
}

.flash-green {
  animation: flash 0.5s ease-out;
}

@keyframes flash-red {
  0% { background-color: rgba(239, 68, 68, 0.2); }
  100% { background-color: transparent; }
}

.flash-red {
  animation: flash-red 0.5s ease-out;
}
