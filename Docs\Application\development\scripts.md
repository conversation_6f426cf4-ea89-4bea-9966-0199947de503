# Development Scripts

### Description [src](../../package.json)

NPM scripts for managing application lifecycle, development workflow, and deployment.

### Application Scripts [src](../../package.json#scripts)

| Script | Command | Description |
| --- | --- | --- |
| app:install | npm install && cd dashboard && npm install | Install all dependencies |
| app:dev | concurrently "npm run collector:dev" "npm run dashboard:server" "npm run dashboard:dev" | Start complete development environment |
| app:build | npm run collector:build && npm run dashboard:build | Build both collector and dashboard |
| app:start | concurrently "npm run collector:start" "npm run dashboard:server" "npm run dashboard:start" | Start production environment |
| app:prod | npm run app:build && npm run app:start | Build and start production |

### Data Collector Scripts [src](../../package.json#scripts)

| Script | Command | Description |
| --- | --- | --- |
| collector:dev | tsx watch src/index.ts | Start with hot reload |
| collector:build | tsc | Build TypeScript to JavaScript |
| collector:start | node dist/index.js | Start built collector |
| collector:prod | npm run collector:build && npm run collector:start | Build and start |
| dev | npm run collector:dev | Alias for collector:dev |

### Dashboard Scripts [src](../../dashboard/package.json#scripts)

| Script | Command | Description |
| --- | --- | --- |
| dashboard:dev | cd dashboard && concurrently "npm run websocket:dev" "npm run frontend:dev" | Start complete dashboard |
| frontend:dev | cd dashboard && next dev -p 3001 | Start Next.js frontend only |
| websocket:dev | cd dashboard && node server.js | Start WebSocket server only |
| dashboard:build | cd dashboard && next build | Build Next.js application |
| dashboard:start | cd dashboard && concurrently "npm run websocket:start" "npm run start" | Start production dashboard |

### Development Workflow

```bash
# First time setup
npm run app:install
cp .env.example .env.local
# Edit .env.local with API credentials

# Daily development
npm run app:dev

# Individual services
npm run collector:dev        # Data collection only
npm run dashboard:dev        # Dashboard only
```

### Service URLs

| Service | URL | Description |
| --- | --- | --- |
| Dashboard | http://localhost:3001 | Main visualization interface |
| WebSocket Server | ws://localhost:8080 | Real-time data distribution |
| Data Collector | Console output | Background data collection |

### Code Quality Scripts [src](../../package.json#scripts)

| Script | Description |
| --- | --- |
| lint | Check code style and errors |
| lint:fix | Fix auto-fixable issues |
| format | Format code with Prettier |
| type-check | Check TypeScript types |
| test | Run all tests |
| test:watch | Run tests in watch mode |

### Production Deployment

```bash
# Build everything
npm run app:build

# Start production
npm run app:start

# Or combined
npm run app:prod
```

### Troubleshooting

| Issue | Solution |
| --- | --- |
| Port conflicts | Check ports 3001, 8080 with netstat |
| Dependency issues | npm run clean && npm run app:install |
| Build failures | npm run type-check for TypeScript errors |
| WebSocket errors | npm run dashboard:server to test server |
