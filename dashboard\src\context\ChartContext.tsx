'use client';

import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react';
import { TimeFrame } from '@/types/market';

interface ChartViewport {
  min: number | null;
  max: number | null;
  timeframe: TimeFrame;
}

interface ChartContextType {
  viewport: ChartViewport;
  setViewport: (viewport: ChartViewport) => void;
  syncCharts: boolean;
  setSyncCharts: (sync: boolean) => void;
  crosshairPosition: { x: number; y: number } | null;
  setCrosshairPosition: (position: { x: number; y: number } | null) => void;
  resetZoom: () => void;
}

const ChartContext = createContext<ChartContextType | undefined>(undefined);

interface ChartProviderProps {
  children: ReactNode;
}

export function ChartProvider({ children }: ChartProviderProps) {
  const [viewport, setViewportState] = useState<ChartViewport>({
    min: null,
    max: null,
    timeframe: '1m',
  });
  
  const [syncCharts, setSyncCharts] = useState(true);
  const [crosshairPosition, setCrosshairPosition] = useState<{ x: number; y: number } | null>(null);

  const setViewport = useCallback((newViewport: ChartViewport) => {
    setViewportState(newViewport);
  }, []);

  const resetZoom = useCallback(() => {
    setViewportState(prev => ({
      ...prev,
      min: null,
      max: null,
    }));
  }, []);

  const value: ChartContextType = {
    viewport,
    setViewport,
    syncCharts,
    setSyncCharts,
    crosshairPosition,
    setCrosshairPosition,
    resetZoom,
  };

  return (
    <ChartContext.Provider value={value}>
      {children}
    </ChartContext.Provider>
  );
}

export function useChart() {
  const context = useContext(ChartContext);
  if (context === undefined) {
    throw new Error('useChart must be used within a ChartProvider');
  }
  return context;
}
