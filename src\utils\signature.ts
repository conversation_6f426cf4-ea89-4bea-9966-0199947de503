import crypto from 'crypto';

export function generateNonce(): string {
  return crypto.randomBytes(16).toString('hex');
}

export function generateTimestamp(): string {
  return Date.now().toString();
}

export function sortQueryParams(params: Record<string, string | number>): string {
  const sortedKeys = Object.keys(params).sort();
  return sortedKeys.map(key => `${key}${params[key]}`).join('');
}

export function compressBody(body: object | null): string {
  if (!body) return '';
  return JSON.stringify(body).replace(/\s/g, '');
}

export function sha256Hash(input: string): string {
  return crypto.createHash('sha256').update(input, 'utf8').digest('hex');
}

export function generateRestSignature(
  nonce: string,
  timestamp: string,
  apiKey: string,
  secretKey: string,
  queryParams: Record<string, string | number> = {},
  body: object | null = null
): string {
  const sortedQueryParams = sortQueryParams(queryParams);
  const compressedBody = compressBody(body);
  
  const digestInput = nonce + timestamp + apiKey + sortedQueryParams + compressedBody;
  const digest = sha256Hash(digestInput);
  
  const signInput = digest + secretKey;
  const sign = sha256Hash(signInput);
  
  return sign;
}

export function generateWebSocketSignature(
  nonce: string,
  timestamp: string,
  apiKey: string,
  secretKey: string,
  params: Record<string, string | number>
): string {
  const sortedKeys = Object.keys(params).filter(key => key !== 'sign').sort();
  const sortedParams = sortedKeys.map(key => `${key}${params[key]}`).join('');
  
  const digestInput = nonce + timestamp + apiKey + sortedParams;
  const digest = sha256Hash(digestInput);
  
  const signInput = digest + secretKey;
  const sign = sha256Hash(signInput);
  
  return sign;
}

export interface SignatureHeaders {
  'api-key': string;
  'nonce': string;
  'timestamp': string;
  'sign': string;
  'Content-Type': string;
}

export function generateSignatureHeaders(
  apiKey: string,
  secretKey: string,
  queryParams: Record<string, string | number> = {},
  body: object | null = null
): SignatureHeaders {
  const nonce = generateNonce();
  const timestamp = generateTimestamp();
  const sign = generateRestSignature(nonce, timestamp, apiKey, secretKey, queryParams, body);
  
  return {
    'api-key': apiKey,
    'nonce': nonce,
    'timestamp': timestamp,
    'sign': sign,
    'Content-Type': 'application/json',
  };
}
