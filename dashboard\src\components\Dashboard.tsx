'use client'

import React, { useState, useEffect, useCallback } from 'react'
import dynamic from 'next/dynamic'
import { DashboardWebSocketClient } from '@/lib/websocketClient'
import PriceChart from './PriceChart'
import OrderBook from './OrderBook'
import TradeFeed from './TradeFeed'
import MarketStats from './MarketStats'
import ConnectionStatus from './ConnectionStatus'
import CVD from './CVD'
import OpenInterest from './OpenInterest'
import LongShortRatio from './LongShortRatio'
import { ChartProvider } from '@/context/ChartContext'
import {
  CandlestickData,
  OrderBookData,
  Trade,
  MarketStats as MarketStatsType,
  TimeFrame,
  DashboardState,
} from '@/types/market'
import { RefreshCw, Settings, Maximize2 } from 'lucide-react'

export default function Dashboard() {
  const [isClient, setIsClient] = useState(false)
  const [wsClient] = useState(() => new DashboardWebSocketClient())
  const [dashboardState, setDashboardState] = useState<DashboardState>({
    isConnected: false,
    lastUpdate: Date.now(),
    selectedTimeframe: '1m',
    autoRefresh: true,
    error: null,
  })

  const [chartData, setChartData] = useState<Map<TimeFrame, CandlestickData[]>>(new Map())
  const [orderBookData, setOrderBookData] = useState<OrderBookData | null>(null)
  const [trades, setTrades] = useState<Trade[]>([])
  const [marketStats, setMarketStats] = useState<MarketStatsType | null>(null)
    const [isLoading, setIsLoading] = useState(true)

  const handleConnection = useCallback(() => {
    setDashboardState(prev => ({
      ...prev,
      isConnected: true,
      error: null,
    }))
    setIsLoading(false)
  }, [])

  const handleDisconnection = useCallback(() => {
    setDashboardState(prev => ({
      ...prev,
      isConnected: false,
    }))
  }, [])

  const handleError = useCallback((error: Error) => {
    setDashboardState(prev => ({
      ...prev,
      error: error.message,
    }))
    setIsLoading(false)
  }, [])

  const handleDepthUpdate = useCallback((data: OrderBookData) => {
    setOrderBookData(data)
    setDashboardState(prev => ({ ...prev, lastUpdate: Date.now() }))
  }, [])

  const handleKlineUpdate = useCallback((data: { interval: TimeFrame; data: CandlestickData[] }) => {
    setChartData(prev => {
      const newMap = new Map(prev)
      newMap.set(data.interval, data.data)
      return newMap
    })
    setDashboardState(prev => ({ ...prev, lastUpdate: Date.now() }))
  }, [])

  const handleTradeUpdate = useCallback((data: Trade[]) => {
    setTrades(prev => {
      const existingIds = new Set(prev.map(t => t.id))
      const newTrades = data.filter(t => !existingIds.has(t.id))
      return [...newTrades, ...prev].slice(0, 100) 
    })
    setDashboardState(prev => ({ ...prev, lastUpdate: Date.now() }))
  }, [])

  const handleStatsUpdate = useCallback((data: MarketStatsType) => {
    setMarketStats(data)
    setDashboardState(prev => ({ ...prev, lastUpdate: Date.now() }))
  }, [])

  useEffect(() => {
    setIsClient(true)
  }, [])

  useEffect(() => {
    if (!isClient) return

    wsClient.on('connected', handleConnection)
    wsClient.on('disconnected', handleDisconnection)
    wsClient.on('error', handleError)
    wsClient.on('depth', handleDepthUpdate)
    wsClient.on('kline', handleKlineUpdate)
    wsClient.on('trade', handleTradeUpdate)
    wsClient.on('stats', handleStatsUpdate)

    wsClient.connect()

    return () => {
      wsClient.disconnect()
    }
  }, [
    isClient,
    wsClient,
    handleConnection,
    handleDisconnection,
    handleError,
    handleDepthUpdate,
    handleKlineUpdate,
    handleTradeUpdate,
    handleStatsUpdate,
  ])

  const handleTimeframeChange = useCallback((timeframe: TimeFrame) => {
    setDashboardState(prev => ({
      ...prev,
      selectedTimeframe: timeframe,
    }))
  }, [])

  const handleRefresh = useCallback(() => {
    if (wsClient.isConnected()) {
      wsClient.send({
        type: 'refresh',
        data: {},
        timestamp: Date.now(),
      })
    }
  }, [wsClient])

  const toggleAutoRefresh = useCallback(() => {
    setDashboardState(prev => ({
      ...prev,
      autoRefresh: !prev.autoRefresh,
    }))
  }, [])

  const currentChartData = chartData.get(dashboardState.selectedTimeframe) || []

  return (
    <ChartProvider>
      <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <h1 className="text-2xl font-bold text-gray-900">
                Bitunix Dashboard
              </h1>
              <ConnectionStatus
                isConnected={dashboardState.isConnected}
                lastUpdate={dashboardState.lastUpdate}
                error={dashboardState.error}
              />
            </div>
            
            <div className="flex items-center space-x-4">
              <button
                onClick={toggleAutoRefresh}
                className={`btn ${
                  dashboardState.autoRefresh ? 'btn-primary' : 'btn-secondary'
                }`}
              >
                <RefreshCw 
                  className={`h-4 w-4 mr-2 ${
                    dashboardState.autoRefresh ? 'animate-spin' : ''
                  }`} 
                />
                Auto Refresh
              </button>
              
              <button
                onClick={handleRefresh}
                disabled={!dashboardState.isConnected}
                className="btn btn-secondary"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </button>
              
              <button className="btn btn-secondary">
                <Settings className="h-4 w-4 mr-2" />
                Settings
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="space-y-6">
          {/* Market Statistics */}
          <MarketStats 
            stats={marketStats} 
            isLoading={isLoading && !marketStats} 
          />

          {/* Main Trading Dashboard Layout */}
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
            {/* Left Column: Charts Stack */}
            <div className="lg:col-span-3 space-y-4">
              {/* Main Price Chart */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                <PriceChart
                  data={currentChartData}
                  timeframe={dashboardState.selectedTimeframe}
                  onTimeframeChange={handleTimeframeChange}
                  isLoading={isLoading && currentChartData.length === 0}
                />
              </div>

              {/* Synchronized Indicator Charts Row */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                {/* CVD Chart - Synchronized with Price Chart */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                  <CVD
                    candlestickData={currentChartData}
                    trades={trades}
                    timeframe={dashboardState.selectedTimeframe}
                  />
                </div>

                {/* Long/Short Ratio Chart - Synchronized with Price Chart */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                  <LongShortRatio
                    orderBook={orderBookData}
                    timeframe={dashboardState.selectedTimeframe}
                    isLoading={isLoading && !orderBookData}
                  />
                </div>
              </div>
            </div>

            {/* Right Column: Order Book and Additional Data */}
            <div className="lg:col-span-1 space-y-4">
              {/* Order Book */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                <OrderBook
                  data={orderBookData}
                  isLoading={isLoading && !orderBookData}
                />
              </div>

              {/* Open Interest */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                <OpenInterest orderBook={orderBookData} />
              </div>
            </div>
          </div>

          {/* Trade Feed */}
          <div className="grid grid-cols-1">
            <TradeFeed
              trades={trades}
              isLoading={isLoading && trades.length === 0}
            />
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-white border-t border-gray-200 mt-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex justify-between items-center text-sm text-gray-500">
            <div>
              <span>Bitunix Dashboard v1.0.0</span>
              <span className="mx-2">•</span>
              <span>Real-time ETHUSDT market data</span>
            </div>
            <div className="flex items-center space-x-4">
              <span>
                Last update: {isClient ? new Date(dashboardState.lastUpdate).toLocaleTimeString() : '--:--:--'}
              </span>
              <div className="flex items-center space-x-2">
                <div className={`w-2 h-2 rounded-full ${
                  dashboardState.isConnected ? 'bg-success-500' : 'bg-danger-500'
                }`} />
                <span>
                  {dashboardState.isConnected ? 'Connected' : 'Disconnected'}
                </span>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
    </ChartProvider>
  )
}
