[Skip to content](https://openapidoc.bitunix.com/doc/websocket/public/kline%20channel.html#VPContent)

Return to top

### Description [​](https://openapidoc.bitunix.com/doc/websocket/public/kline%20channel.html\#description)

Retrieve the candlesticks data of a symbol. Data will be pushed every 500 ms.

The channel will push a snapshot after successful subscription, followed by subsequent updates.

**To switch K-line intervals without disconnecting the WebSocket, you must first send an "unsubscribe" command to cancel the previous K-line subscription before subscribing to the new interval. For example, if you are currently subscribed to `mark_kline_1min` and want to switch to `mark_kline_15min`, you must first unsubscribe from `mark_kline_1min` and then subscribe to `mark_kline_15min`.**

### Request Parameters [​](https://openapidoc.bitunix.com/doc/websocket/public/kline%20channel.html\#request-parameters)

| Parameter | Type | Required | Description |
| --- | --- | --- | --- |
| op | String | Yes | Operation, subscribe unsubscribe |
| args | List<Object> | Yes | List of channels to request subscription |
| > ch | String | Yes | Channel name, The subscription channel is: Price Type _klineTime Interval；The price types include market price and marked price；market_\_ _kline_\_ _1min,mark_\_ _kline_\_ _1min,market_\_ _kline_\_ _3min,mark_\_ _kline_\_ _3min,market_\_ _kline_\_ _5min,mark_\_ _kline_\_ _5min,market_\_ _kline_\_ _15min,mark_\_ _kline_\_ _15min,market_\_ _kline_\_ _30min,mark_\_ _kline_\_ _30min,market_\_ _kline_\_ _60min,mark_\_ _kline_\_ _60min,market_\_ _kline_\_ _2h,mark_\_ _kline_\_ _2h,market_\_ _kline_\_ _4h,mark_\_ _kline_\_ _4h,market_\_ _kline_\_ _6h,mark_\_ _kline_\_ _6h,market_\_ _kline_\_ _8h,mark_\_ _kline_\_ _8h,market_\_ _kline_\_ _12h,mark_\_ _kline_\_ _12h,market_\_ _kline_\_ _1day,mark_\_ _kline_\_ _1day,market_\_ _kline_\_ _3day,mark_\_ _kline_\_ _3day,market_\_ _kline_\_ _1week,mark_\_ _kline_\_ _1week,market_\_ _kline_\_ _1month,mark_\_ _kline_\_ _1month_ |
| > symbol | String | Yes | Product ID E.g. ETHUSDT |

request example:

json

```
{
    "op":"subscribe",
    "args":[\
        {\
            "symbol":"BTCUSDT",\
            "ch":"market_kline_1min"\
        }\
    ]
}
```

### Push Parameters [​](https://openapidoc.bitunix.com/doc/websocket/public/kline%20channel.html\#push-parameters)

| Parameter | Type | Description |
| --- | --- | --- |
| ch | String | Channel name |
| symbol | String | Product ID E.g. ETHUSDT |
| ts | int64 | Time stamp |
| data | List<String> | Subscription data |
| > o | String | Opening price |
| > h | String | Highest price |
| > l | String | Lowest price |
| > c | String | Closing price |
| > b | String | Trading volume of the coin |
| > q | String | Trading volume of quote currency |

push data:

json

```
{
  "ch": "mark_kline_1min",
  "symbol": "BNBUSDT",
  "ts": 1732178884994,
  "data":{
      "o": "0.0010",
      "c": "0.0020",
      "h": "0.0025",
      "l": "0.0015",
      "b": "1.01",
      "q": "1.09"
  }
}
```