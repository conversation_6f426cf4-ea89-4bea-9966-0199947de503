'use client'

import React, { useState, useEffect, useMemo } from 'react'
import { Line } from 'react-chartjs-2'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  TimeScale,
} from 'chart.js'
import 'chartjs-adapter-date-fns'
import { Trade, TimeFrame, CandlestickData } from '@/types/market'
import { useChart } from '@/context/ChartContext'

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  TimeScale
)

interface CVDProps {
  candlestickData: CandlestickData[]
  trades: Trade[]
  timeframe: TimeFrame
}

interface CVDDataPoint {
  timestamp: number
  cvd: number
  buyVolume: number
  sellVolume: number
}

interface CVDData {
  current: number
  change: number
  changePercent: number
  buyVolume: number
  sellVolume: number
  netVolume: number
  chartData: CVDDataPoint[]
}

export default function CVD({ candlestickData, trades, timeframe }: CVDProps) {
  const { viewport } = useChart();
  const [cvdData, setCvdData] = useState<CVDData>({
    current: 0,
    change: 0,
    changePercent: 0,
    buyVolume: 0,
    sellVolume: 0,
    netVolume: 0,
    chartData: [],
  })

  const cvdChartData = useMemo(() => {
    if (!candlestickData || candlestickData.length === 0) return []

    const dataPoints: CVDDataPoint[] = []
    let runningCVD = 0

    // Calculate CVD  historical candlestick data
    candlestickData.forEach((candle) => {
      const priceChange = candle.c - candle.o
      const volume = candle.volume

      let buyVolume = 0
      let sellVolume = 0

      if (priceChange > 0) {
        const buyRatio = 0.6 + Math.min((priceChange / candle.o) * 10, 0.3)
        buyVolume = volume * buyRatio
        sellVolume = volume - buyVolume
      } else if (priceChange < 0) {
        const sellRatio = 0.6 + Math.min(Math.abs(priceChange / candle.o) * 10, 0.3)
        sellVolume = volume * sellRatio
        buyVolume = volume - sellVolume
      } else {
        buyVolume = volume * 0.5
        sellVolume = volume * 0.5
      }

      const upperWick = candle.h - Math.max(candle.o, candle.c)
      const lowerWick = Math.min(candle.o, candle.c) - candle.l
      const bodySize = Math.abs(candle.c - candle.o)
      const totalRange = candle.h - candle.l

      if (totalRange > 0) {
        if (upperWick > bodySize * 0.5) {
          const wickAdjustment = (upperWick / totalRange) * volume * 0.15
          sellVolume += wickAdjustment
          buyVolume = Math.max(0, buyVolume - wickAdjustment)
        }

        if (lowerWick > bodySize * 0.5) {
          const wickAdjustment = (lowerWick / totalRange) * volume * 0.15
          buyVolume += wickAdjustment
          sellVolume = Math.max(0, sellVolume - wickAdjustment)
        }
      }

      buyVolume = Math.max(0, buyVolume)
      sellVolume = Math.max(0, sellVolume)
      const totalEstimated = buyVolume + sellVolume
      if (totalEstimated > 0) {
        buyVolume = (buyVolume / totalEstimated) * volume
        sellVolume = (sellVolume / totalEstimated) * volume
      }

      const cvdDelta = buyVolume - sellVolume
      runningCVD += cvdDelta

      dataPoints.push({
        timestamp: candle.x,
        cvd: runningCVD,
        buyVolume,
        sellVolume,
      })
    })

    return dataPoints
  }, [candlestickData])

  useEffect(() => {
    if (cvdChartData.length === 0) return

    const latestPoint = cvdChartData[cvdChartData.length - 1]
    const previousPoint = cvdChartData[cvdChartData.length - 2]

    const current = latestPoint.cvd
    const change = previousPoint ? current - previousPoint.cvd : 0
    const changePercent = previousPoint && previousPoint.cvd !== 0 ?
      (change / Math.abs(previousPoint.cvd)) * 100 : 0

    const recentPoints = cvdChartData.slice(-10)
    const totalBuyVolume = recentPoints.reduce((sum, point) => sum + point.buyVolume, 0)
    const totalSellVolume = recentPoints.reduce((sum, point) => sum + point.sellVolume, 0)
    const netVolume = totalBuyVolume - totalSellVolume

    setCvdData({
      current,
      change,
      changePercent,
      buyVolume: totalBuyVolume,
      sellVolume: totalSellVolume,
      netVolume,
      chartData: cvdChartData,
    })
  }, [cvdChartData])

  const formatVolume = (volume: number): string => {
    if (volume >= 1000000) {
      return `${(volume / 1000000).toFixed(2)}M`
    } else if (volume >= 1000) {
      return `${(volume / 1000).toFixed(2)}K`
    }
    return volume.toFixed(2)
  }

  const getCVDColor = (): string => {
    if (cvdData.current > 0) return 'text-success-600'
    if (cvdData.current < 0) return 'text-danger-600'
    return 'text-gray-600'
  }

  const getChangeColor = (): string => {
    if (cvdData.change > 0) return 'text-success-600'
    if (cvdData.change < 0) return 'text-danger-600'
    return 'text-gray-600'
  }

  const chartData = {
    labels: cvdData.chartData.map(point => new Date(point.timestamp)),
    datasets: [
      {
        label: 'CVD',
        data: cvdData.chartData.map(point => point.cvd),
        borderColor: cvdData.current >= 0 ? '#10b981' : '#ef4444',
        backgroundColor: cvdData.current >= 0 ? 'rgba(16, 185, 129, 0.1)' : 'rgba(239, 68, 68, 0.1)',
        borderWidth: 2,
        fill: true,
        tension: 0.1,
        pointRadius: 0,
        pointHoverRadius: 4,
      },
    ],
  }

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        mode: 'index' as const,
        intersect: false,
        callbacks: {
          label: (context: any) => {
            const value = context.parsed.y
            return `CVD: ${formatVolume(value)}`
          },
        },
      },
    },
    scales: {
      x: {
        type: 'time' as const,
        time: {
          displayFormats: {
            minute: 'HH:mm',
            hour: 'HH:mm',
          },
        },
        grid: {
          display: false,
        },
        ticks: {
          maxTicksLimit: 6,
        },
        // Synchronize with main chart viewport
        min: viewport.timeframe === timeframe && viewport.min !== null ? viewport.min : undefined,
        max: viewport.timeframe === timeframe && viewport.max !== null ? viewport.max : undefined,
      },
      y: {
        grid: {
          color: 'rgba(0, 0, 0, 0.1)',
        },
        ticks: {
          callback: (value: any) => formatVolume(value),
        },
      },
    },
    interaction: {
      mode: 'index' as const,
      intersect: false,
    },
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">
          CVD (Cumulative Volume Delta)
        </h3>
        <div className="text-xs text-gray-500">
          {timeframe} • {cvdData.chartData.length} periods
        </div>
      </div>

      {/* CVD Chart */}
      <div className="mb-4" style={{ height: '200px' }}>
        {cvdData.chartData.length > 0 ? (
          <Line data={chartData} options={chartOptions} />
        ) : (
          <div className="flex items-center justify-center h-full text-gray-500">
            Loading CVD data...
          </div>
        )}
      </div>

      {/* Main CVD Value */}
      <div className="mb-4">
        <div className={`text-2xl font-bold ${getCVDColor()}`}>
          {cvdData.current >= 0 ? '+' : ''}{formatVolume(cvdData.current)}
        </div>
        <div className={`text-sm ${getChangeColor()}`}>
          {cvdData.change >= 0 ? '+' : ''}{formatVolume(cvdData.change)}
          {cvdData.changePercent !== 0 && (
            <span className="ml-1">
              ({cvdData.changePercent >= 0 ? '+' : ''}{cvdData.changePercent.toFixed(2)}%)
            </span>
          )}
        </div>
      </div>

      {/* Volume Breakdown */}
      <div className="grid grid-cols-2 gap-4 mb-4">
        <div className="text-center">
          <div className="text-xs text-gray-500 mb-1">Buy Volume</div>
          <div className="text-sm font-semibold text-success-600">
            {formatVolume(cvdData.buyVolume)}
          </div>
        </div>
        <div className="text-center">
          <div className="text-xs text-gray-500 mb-1">Sell Volume</div>
          <div className="text-sm font-semibold text-danger-600">
            {formatVolume(cvdData.sellVolume)}
          </div>
        </div>
      </div>

      {/* Net Volume */}
      <div className="border-t border-gray-200 pt-3">
        <div className="flex justify-between items-center">
          <span className="text-xs text-gray-500">Net Volume:</span>
          <span className={`text-sm font-semibold ${
            cvdData.netVolume > 0 ? 'text-success-600' : 
            cvdData.netVolume < 0 ? 'text-danger-600' : 'text-gray-600'
          }`}>
            {cvdData.netVolume >= 0 ? '+' : ''}{formatVolume(cvdData.netVolume)}
          </span>
        </div>
      </div>

      {/* CVD Explanation */}
      <div className="mt-3 text-xs text-gray-400">
        CVD tracks buying vs selling pressure. Positive = more buying, Negative = more selling.
      </div>
    </div>
  )
}
