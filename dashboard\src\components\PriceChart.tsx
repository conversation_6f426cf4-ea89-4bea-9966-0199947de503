'use client'

import React, { useEffect, useRef, useState, useCallback } from 'react'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  TimeScale,
  BarElement,
  LineElement,
  PointElement,
  Title,
  Tooltip,
  Legend,
  ChartOptions,
} from 'chart.js'
import zoomPlugin from 'chartjs-plugin-zoom'
import { Chart } from 'react-chartjs-2'
import 'chartjs-adapter-date-fns'
import { CandlestickData, TimeFrame } from '@/types/market'
import { format } from 'date-fns'
import { useChart } from '@/context/ChartContext'
import { ZoomIn, ZoomOut, RotateCcw } from 'lucide-react'

ChartJS.register(
  CategoryScale,
  LinearScale,
  TimeScale,
  BarElement,
  LineElement,
  PointElement,
  Title,
  Tooltip,
  Legend,
  zoomPlugin
)

interface PriceChartProps {
  data: CandlestickData[]
  timeframe: TimeFrame
  onTimeframeChange: (timeframe: TimeFrame) => void
  isLoading?: boolean
}

const timeframes: { value: TimeFrame; label: string }[] = [
  { value: '1m', label: '1m' },
  { value: '3m', label: '3m' },
  { value: '5m', label: '5m' },
  { value: '15m', label: '15m' },
]

export default function PriceChart({
  data,
  timeframe,
  onTimeframeChange,
  isLoading = false
}: PriceChartProps) {
  const chartRef = useRef<ChartJS<'line'>>(null)
  const [chartData, setChartData] = useState<any>(null)
  const { viewport, setViewport, resetZoom, syncCharts } = useChart()

  useEffect(() => {
    if (!data || data.length === 0) return

    const sortedData = [...data].sort((a, b) => a.x - b.x)
    const priceData = sortedData.map(candle => ({
      x: candle.x,
      y: candle.c,
    }))

    const volumeData = sortedData.map(candle => ({
      x: candle.x,
      y: candle.volume,
    }))

    setChartData({
      datasets: [
        {
          label: 'ETHUSDT Price',
          data: priceData,
          borderColor: 'rgb(59, 130, 246)',
          backgroundColor: 'rgba(59, 130, 246, 0.1)',
          borderWidth: 2,
          fill: true,
          tension: 0.1,
          pointRadius: 0,
          pointHoverRadius: 4,
        },
      ],
    })
  }, [data])

  // Handle zoom events to sync with other charts
  const handleZoom = useCallback((chart: any) => {
    if (syncCharts && chart.scales.x) {
      const { min, max } = chart.scales.x;
      setViewport({
        min: min,
        max: max,
        timeframe: timeframe,
      });
    }
  }, [syncCharts, setViewport, timeframe]);

  const options: ChartOptions<'line'> = {
    responsive: true,
    maintainAspectRatio: false,
    interaction: {
      intersect: false,
      mode: 'index',
    },
    scales: {
      x: {
        type: 'time',
        time: {
          unit: timeframe === '1m' ? 'minute' : 'hour',
          displayFormats: {
            minute: 'HH:mm',
            hour: 'HH:mm',
          },
        },
        title: {
          display: true,
          text: 'Time',
        },
        min: viewport.timeframe === timeframe && viewport.min !== null ? viewport.min : undefined,
        max: viewport.timeframe === timeframe && viewport.max !== null ? viewport.max : undefined,
      },
      y: {
        title: {
          display: true,
          text: 'Price (USDT)',
        },
        position: 'right',
      },
    },
    plugins: {
      zoom: {
        limits: {
          x: {min: 'original', max: 'original'},
        },
        zoom: {
          wheel: {
            enabled: true,
            speed: 0.1,
          },
          pinch: {
            enabled: true,
          },
          drag: {
            enabled: false,
          },
          mode: 'x',
          onZoomComplete: handleZoom,
        },
        pan: {
          enabled: true,
          mode: 'x',
          threshold: 10,
          onPanComplete: handleZoom,
        },
      },
      legend: {
        display: false,
      },
      title: {
        display: true,
        text: `ETHUSDT - ${timeframe}`,
        font: {
          size: 16,
          weight: 'bold',
        },
      },
      tooltip: {
        callbacks: {
          title: (context) => {
            return format(new Date(context[0].parsed.x), 'MMM dd, HH:mm:ss')
          },
          label: (context) => {
            const candle = data[context.dataIndex]
            if (!candle) return ''

            return [
              `Price: $${candle.c.toFixed(2)}`,
              `Open: $${candle.o.toFixed(2)}`,
              `High: $${candle.h.toFixed(2)}`,
              `Low: $${candle.l.toFixed(2)}`,
              `Volume: ${candle.volume.toFixed(4)} ETH`,
            ]
          },
        },
      },
    },
    animation: {
      duration: 0, 
    },
  }

  return (
    <div className="card">
      <div className="card-header">
        <h3 className="card-title">Price Chart</h3>
        <div className="flex items-center space-x-4">
          {/* Zoom Controls */}
          <div className="flex space-x-1">
            <button
              onClick={() => {
                if (chartRef.current) {
                  const chart = chartRef.current;
                  const zoomLevel = chart.getZoomLevel ? chart.getZoomLevel() * 1.2 : 1.2;
                  chart.zoom && chart.zoom(zoomLevel);
                }
              }}
              className="p-1 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded"
              title="Zoom In"
            >
              <ZoomIn className="h-4 w-4" />
            </button>
            <button
              onClick={() => {
                if (chartRef.current) {
                  const chart = chartRef.current;
                  const zoomLevel = chart.getZoomLevel ? chart.getZoomLevel() * 0.8 : 0.8;
                  chart.zoom && chart.zoom(zoomLevel);
                }
              }}
              className="p-1 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded"
              title="Zoom Out"
            >
              <ZoomOut className="h-4 w-4" />
            </button>
            <button
              onClick={() => {
                if (chartRef.current) {
                  chartRef.current.resetZoom && chartRef.current.resetZoom();
                }
                resetZoom();
              }}
              className="p-1 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded"
              title="Reset Zoom"
            >
              <RotateCcw className="h-4 w-4" />
            </button>
          </div>

          {/* Timeframe Selection */}
          <div className="flex space-x-2">
            {timeframes.map(({ value, label }) => (
              <button
                key={value}
                onClick={() => onTimeframeChange(value)}
                className={`px-3 py-1 text-sm font-medium rounded-md transition-colors ${
                  timeframe === value
                    ? 'bg-primary-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {label}
              </button>
            ))}
          </div>
        </div>
      </div>
      
      <div className="relative">
        {isLoading && (
          <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
          </div>
        )}
        
        <div className="chart-container">
          {chartData ? (
            <Chart
              ref={chartRef}
              type="line"
              data={chartData}
              options={options}
            />
          ) : (
            <div className="flex items-center justify-center h-96 text-gray-500">
              <div className="text-center">
                <div className="animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-32 mx-auto mb-2"></div>
                  <div className="h-4 bg-gray-200 rounded w-24 mx-auto"></div>
                </div>
                <p className="mt-4">Loading chart data...</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
