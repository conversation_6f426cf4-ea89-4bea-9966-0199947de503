{"name": "bitunix-data-collector", "version": "1.0.0", "description": "TypeScript data collection system for Bitunix crypto exchange API", "main": "dist/index.js", "scripts": {"build": "tsc", "dev": "tsx watch src/index.ts", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "type-check": "tsc --noEmit", "clean": "<PERSON><PERSON><PERSON> dist", "prebuild": "npm run clean", "collector:dev": "npm run dev", "collector:build": "npm run build", "collector:start": "npm run start", "collector:prod": "npm run build && npm run start", "dashboard:dev": "cd dashboard && next dev -p 3001", "dashboard:build": "cd dashboard && next build", "dashboard:start": "cd dashboard && next start -p 3001", "dashboard:server": "cd dashboard && node server.js", "dashboard:prod": "npm run dashboard:build && npm run dashboard:start", "app:install": "pnpm install", "app:build": "pnpm run build && pnpm run dashboard:build", "app:dev": "concurrently \"pnpm run collector:dev\" \"pnpm run dashboard:server\" \"pnpm run dashboard:dev\" \"pnpm run browser:launch\"", "browser:launch": "node scripts/launch-browser.js", "app:start": "concurrently \"pnpm run collector:start\" \"pnpm run dashboard:server\" \"pnpm run dashboard:start\"", "app:prod": "pnpm run app:build && pnpm run app:start", "services:start": "concurrently \"pnpm run collector:dev\" \"pnpm run dashboard:server\"", "services:stop": "pkill -f \"tsx watch\" && pkill -f \"node server.js\" && pkill -f \"next\"", "health:check": "node -e \"console.log('Health check - Services should be running on:'); console.log('Data Collector: Running in terminal'); console.log('Dashboard: http://localhost:3001'); console.log('WebSocket: ws://localhost:8080');\""}, "keywords": ["bitunix", "crypto", "exchange", "api", "data-collection", "typescript"], "author": "<PERSON>", "license": "MIT", "devDependencies": {"@types/jest": "^29.5.8", "@types/node": "^20.9.0", "@types/ws": "^8.5.10", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "concurrently": "^8.2.2", "eslint": "^8.54.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "jest": "^29.7.0", "prettier": "^3.1.0", "rimraf": "^5.0.5", "ts-jest": "^29.1.1", "tsx": "^4.6.0", "typescript": "^5.3.2"}, "dependencies": {"@playwright/test": "^1.54.1", "@tailwindcss/forms": "^0.5.7", "@tanstack/react-query": "^5.83.0", "@tremor/react": "^3.14.1", "@types/react": "^18.2.38", "@types/react-dom": "^18.2.17", "autoprefixer": "^10.4.16", "axios": "^1.6.2", "chokidar": "^3.5.3", "clsx": "^2.0.0", "date-fns": "^2.30.0", "dotenv": "^16.3.1", "jotai": "^2.6.0", "lightweight-charts": "^4.2.3", "lucide-react": "^0.294.0", "next": "^14.0.4", "postcss": "^8.4.32", "react": "^18.2.0", "react-dom": "^18.2.0", "react-grid-layout": "^1.4.4", "react-resizable-panels": "^2.1.9", "react-use-websocket": "^4.5.0", "react-virtualized-auto-sizer": "^1.0.24", "recharts": "^2.8.0", "tailwindcss": "^3.3.6", "winston": "^3.11.0", "ws": "^8.18.3", "zod": "^3.22.4", "zustand": "^4.4.7"}}