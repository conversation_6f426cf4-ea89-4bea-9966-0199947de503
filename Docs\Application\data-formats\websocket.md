# WebSocket Messages

### Description [src](../../dashboard/server.js)

Real-time message formats for WebSocket communication between server and dashboard clients.

### Message Structure [src](../../dashboard/server.js#messageFormat)

```json
{
  "type": "depth|kline|trade|price|stats|error",
  "data": {},
  "timestamp": 1703123456789
}
```

### Depth Message [src](../../dashboard/src/lib/dataProcessor.ts#depthMessage)

```json
{
  "type": "depth",
  "data": {
    "bids": [
      { "price": 2984.50, "size": 1.234, "total": 1.234 },
      { "price": 2984.25, "size": 0.567, "total": 1.801 }
    ],
    "asks": [
      { "price": 2984.75, "size": 0.890, "total": 0.890 },
      { "price": 2985.00, "size": 1.456, "total": 2.346 }
    ]
  },
  "timestamp": 1703123456789
}
```

### Kline Message [src](../../dashboard/src/lib/dataProcessor.ts#klineMessage)

```json
{
  "type": "kline",
  "data": {
    "interval": "1m",
    "data": [
      {
        "x": 1703123400000,
        "o": 2984.25,
        "h": 2985.50,
        "l": 2983.75,
        "c": 2984.80,
        "volume": 12.345
      }
    ]
  },
  "timestamp": 1703123456789
}
```

### Trade Message [src](../../dashboard/src/lib/dataProcessor.ts#tradeMessage)

```json
{
  "type": "trade",
  "data": [
    {
      "id": "trade_123456",
      "price": 2984.50,
      "volume": 0.123,
      "side": "buy",
      "timestamp": 1703123456789
    }
  ],
  "timestamp": 1703123456789
}
```

### Price Message [src](../../dashboard/src/lib/dataProcessor.ts#priceMessage)

```json
{
  "type": "price",
  "data": {
    "symbol": "ETHUSDT",
    "price": 2984.50,
    "change24h": 45.25,
    "changePercent24h": 1.54,
    "timestamp": 1703123456789
  },
  "timestamp": 1703123456789
}
```

### Stats Message [src](../../dashboard/src/lib/dataProcessor.ts#statsMessage)

```json
{
  "type": "stats",
  "data": {
    "currentPrice": 2984.50,
    "change24h": 45.25,
    "changePercent24h": 1.54,
    "volume24h": 1234.567,
    "high24h": 2995.00,
    "low24h": 2970.25,
    "spread": 0.25,
    "lastUpdate": 1703123456789
  },
  "timestamp": 1703123456789
}
```

### Error Message [src](../../dashboard/server.js#errorMessage)

```json
{
  "type": "error",
  "data": {
    "message": "Error description",
    "code": "ERROR_CODE",
    "timestamp": 1703123456789
  },
  "timestamp": 1703123456789
}
```

### Connection Messages [src](../../dashboard/server.js#connectionMessages)

| Message Type | Description |
| --- | --- |
| connection | Initial connection established |
| heartbeat | Keep-alive ping/pong |
| shutdown | Server shutdown notification |
| reconnect | Reconnection required |

### Message Frequency [src](../../dashboard/server.js#frequency)

| Message Type | Frequency | Description |
| --- | --- | --- |
| depth | 1 second | Order book updates |
| kline | 5 seconds | Candlestick data |
| trade | Real-time | Individual trade executions |
| price | Real-time | Price updates |
| stats | 1 minute | Market statistics |
