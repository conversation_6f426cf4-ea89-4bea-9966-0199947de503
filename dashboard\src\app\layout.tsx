import type { Metadata } from 'next'
import './globals.css'
import ErrorBoundary from '@/lib/errorBoundary'
import Providers from '@/components/Providers'

export const metadata: Metadata = {
  title: 'Bitunix Dashboard - Real-time Market Data',
  description: 'Real-time visualization dashboard for Bitunix ETHUSDT market data',
  keywords: 'bitunix, cryptocurrency, trading, dashboard, real-time, ETHUSDT',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className="min-h-screen bg-gray-50">
        <ErrorBoundary>
          <Providers>
            <div className="min-h-screen">
              {children}
            </div>
          </Providers>
        </ErrorBoundary>
      </body>
    </html>
  )
}
