import { EventEmitter } from 'events';
import { BitunixApiClient } from '@/api/BitunixApiClient';
import { BitunixWebSocketClient } from '@/api/BitunixWebSocketClient';
import { DataStorage } from '@/storage/DataStorage';
import { RateLimiter } from '@/utils/rateLimiter';
import { logger } from '@/utils/logger';
import { 
  BitunixConfig, 
  KlineInterval,
  MarketPriceMessage,
  TradeMessage 
} from '@/types/bitunix';

export interface DataCollectorConfig {
  symbol: string;
  klineIntervals: KlineInterval[];
  depthLimit: string;
  restDataInterval: number;
  depthCollectionInterval: number;
  klineCollectionInterval: number;
  enableWebSocket: boolean;
  enableRestApi: boolean;
}

export interface DataCollectorEvents {
  started: () => void;
  stopped: () => void;
  error: (error: Error) => void;
  dataCollected: (type: string, data: any) => void;
}

export class BitunixDataCollector extends EventEmitter {
  private readonly apiClient: BitunixApiClient;
  private readonly wsClient: BitunixWebSocketClient;
  private readonly storage: DataStorage;
  private readonly rateLimiter: RateLimiter;
  private readonly config: DataCollectorConfig;
  
  private isRunning = false;
  private restDataInterval: NodeJS.Timeout | null = null;
  private depthInterval: NodeJS.Timeout | null = null;
  private klineInterval: NodeJS.Timeout | null = null;
  private readonly intervals: NodeJS.Timeout[] = [];

  constructor(
    bitunixConfig: BitunixConfig,
    collectorConfig: DataCollectorConfig,
    storageDir?: string
  ) {
    super();
    
    this.config = collectorConfig;
    this.apiClient = new BitunixApiClient(bitunixConfig);
    this.wsClient = new BitunixWebSocketClient(bitunixConfig);
    this.storage = new DataStorage(storageDir);
    this.rateLimiter = new RateLimiter(9, 1000); // Use 9/10 requests to leave buffer
    
    this.setupWebSocketHandlers();
  }

  private setupWebSocketHandlers(): void {
    this.wsClient.on('connected', () => {
      logger.info('WebSocket connected, subscribing to channels');
      this.subscribeToChannels();
    });

    this.wsClient.on('disconnected', () => {
      logger.warn('WebSocket disconnected');
    });

    this.wsClient.on('error', (error: Error) => {
      logger.error('WebSocket error:', error);
      this.emit('error', error);
    });

    this.wsClient.on('marketPrice', async (data: MarketPriceMessage) => {
      try {
        if (data.symbol === this.config.symbol) {
          await this.storage.storeMarketPriceData(data.symbol, data.data);
          this.emit('dataCollected', 'marketPrice', data);
        }
      } catch (error) {
        logger.error('Failed to store market price data:', error);
      }
    });

    this.wsClient.on('trade', async (data: TradeMessage) => {
      try {
        if (data.symbol === this.config.symbol) {
          await this.storage.storeTradeData(data.symbol, data.data);
          this.emit('dataCollected', 'trade', data);
        }
      } catch (error) {
        logger.error('Failed to store trade data:', error);
      }
    });
  }

  public async start(): Promise<void> {
    if (this.isRunning) {
      logger.warn('Data collector is already running');
      return;
    }

    try {
      logger.info('Starting Bitunix data collector', this.config);

      const isConnected = await this.apiClient.ping();
      if (!isConnected) {
        throw new Error('Failed to connect to Bitunix API');
      }

      this.isRunning = true;

      // Collect historical data on startup if not present
      await this.collectHistoricalDataIfNeeded();

      if (this.config.enableWebSocket) {
        await this.wsClient.connect();
      }

      if (this.config.enableRestApi) {
        this.startOptimizedDataCollection();
      }

      this.emit('started');
      logger.info('Bitunix data collector started successfully');

    } catch (error) {
      this.isRunning = false;
      logger.error('Failed to start data collector:', error);
      this.emit('error', error as Error);
      throw error;
    }
  }

  private subscribeToChannels(): void {
    if (!this.config.enableWebSocket) return;

    // Subscribe to real-time channels
    this.wsClient.subscribe(this.config.symbol, 'price');
    this.wsClient.subscribe(this.config.symbol, 'trade');

    // Note: Depth data is collected via REST API for consistency and reliability
    // WebSocket depth updates can be inconsistent, so we use REST API at 1-second intervals
    logger.info('Subscribed to WebSocket channels: price, trade');
  }

  private startOptimizedDataCollection(): void {
    this.depthInterval = setInterval(async () => {
      if (!this.isRunning) return;

      const rateLimitInfo = this.apiClient.getRateLimitInfo();
      if (rateLimitInfo.requestsRemaining < 1) {
        logger.warn('Rate limit reached, skipping depth collection');
        return;
      }

      try {
        await this.collectDepthData();
      } catch (error) {
        logger.error('Error during depth data collection:', error);
      }
    }, this.config.depthCollectionInterval);

    this.startStaggeredKlineCollection();

    logger.info('Started optimized data collection', {
      depthInterval: this.config.depthCollectionInterval,
      klineIntervals: this.config.klineIntervals,
      maxRequestsPerSecond: 10
    });
  }

  private startStaggeredKlineCollection(): void {
    const intervalConfigs = [
      { interval: '1m', frequency: 60000 },   // Every 1 minute
      { interval: '3m', frequency: 180000 },  // Every 3 minutes
      { interval: '5m', frequency: 300000 },  // Every 5 minutes
      { interval: '15m', frequency: 900000 }, // Every 15 minutes
    ];

    intervalConfigs.forEach((config, index) => {
      if (this.config.klineIntervals.includes(config.interval as any)) {
        const startDelay = index * 1000; // 2 second stagger

        setTimeout(() => {
          const intervalTimer = setInterval(async () => {
            if (!this.isRunning) return;

            const rateLimitInfo = this.apiClient.getRateLimitInfo();
            if (rateLimitInfo.requestsRemaining < 1) {
              logger.warn(`Rate limit reached, skipping ${config.interval} kline collection`);
              return;
            }

            try {
              await this.collectSingleKlineData(config.interval as any);
            } catch (error) {
              logger.error(`Error collecting ${config.interval} kline data:`, error);
            }
          }, config.frequency);

          this.intervals.push(intervalTimer);
        }, startDelay);
      }
    });
  }

  private async collectDepthData(): Promise<void> {
    try {
      await this.rateLimiter.waitForSlot();
      
      const depthResponse = await this.apiClient.getDepth(
        this.config.symbol, 
        this.config.depthLimit as any
      );
      
      await this.storage.storeDepthData(
        this.config.symbol, 
        depthResponse.data, 
        this.config.depthLimit
      );
      
      this.emit('dataCollected', 'depth', depthResponse.data);
      logger.debug('Collected depth data', { symbol: this.config.symbol });
      
    } catch (error) {
      logger.error('Failed to collect depth data:', error);
    }
  }

  private async collectHistoricalDataIfNeeded(): Promise<void> {
    logger.info('Checking for historical data...');

    const sevenDaysAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);

    for (const interval of this.config.klineIntervals) {
      try {
        // Check if we have sufficient historical data
        const hasHistoricalData = await this.storage.hasHistoricalData(
          this.config.symbol,
          interval,
          sevenDaysAgo
        );

        if (!hasHistoricalData) {
          logger.info(`Collecting 7 days of historical data for ${interval}...`);
          await this.collectHistoricalKlineData(interval, sevenDaysAgo);
        } else {
          logger.info(`Historical data for ${interval} already exists, skipping...`);
        }
      } catch (error) {
        logger.error(`Failed to collect historical data for ${interval}:`, error);
      }
    }
  }

  private async collectHistoricalKlineData(interval: KlineInterval, startTime: number): Promise<void> {
    try {
      await this.rateLimiter.waitForSlot();

      // Fetch 1000 candles for comprehensive historical data
      const klineResponse = await this.apiClient.getKline(
        this.config.symbol,
        interval,
        startTime,
        undefined,
        1000  // Maximum allowed by most exchanges
      );

      await this.storage.storeKlineData(
        this.config.symbol,
        interval,
        klineResponse.data
      );

      this.emit('dataCollected', 'kline', { interval, data: klineResponse.data });
      logger.info(`Collected ${klineResponse.data.length} historical candles for ${interval}`);

    } catch (error) {
      logger.error('Failed to collect historical kline data:', { interval, error });
    }
  }

  private async collectSingleKlineData(interval: KlineInterval): Promise<void> {
    try {
      await this.rateLimiter.waitForSlot();

      // Fetch more recent data for real-time updates (increased from 10 to 100)
      const klineResponse = await this.apiClient.getKline(
        this.config.symbol,
        interval,
        undefined,
        undefined,
        100  // Increased for better chart analysis
      );

      await this.storage.storeKlineData(
        this.config.symbol,
        interval,
        klineResponse.data
      );

      this.emit('dataCollected', 'kline', { interval, data: klineResponse.data });
      logger.debug('Collected kline data', { symbol: this.config.symbol, interval });

    } catch (error) {
      logger.error('Failed to collect kline data:', { interval, error });
    }
  }

  public async stop(): Promise<void> {
    if (!this.isRunning) {
      logger.warn('Data collector is not running');
      return;
    }

    this.isRunning = false;

    if (this.restDataInterval) {
      clearInterval(this.restDataInterval);
      this.restDataInterval = null;
    }

    if (this.depthInterval) {
      clearInterval(this.depthInterval);
      this.depthInterval = null;
    }

    if (this.klineInterval) {
      clearInterval(this.klineInterval);
      this.klineInterval = null;
    }

    this.intervals.forEach(interval => clearInterval(interval));
    this.intervals.length = 0;

    this.wsClient.disconnect();

    this.emit('stopped');
    logger.info('Bitunix data collector stopped');
  }

  public getStatus(): {
    isRunning: boolean;
    config: DataCollectorConfig;
    rateLimitInfo: any;
    wsConnected: boolean;
  } {
    return {
      isRunning: this.isRunning,
      config: this.config,
      rateLimitInfo: this.apiClient.getRateLimitInfo(),
      wsConnected: this.wsClient.isConnected(),
    };
  }

  public async getStorageStats(): Promise<any> {
    return await this.storage.getStorageStats();
  }
}
