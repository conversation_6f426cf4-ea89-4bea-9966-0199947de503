/** @type {import('next').NextConfig} */
const nextConfig = {
  typescript: {
    ignoreBuildErrors: false,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  // Enable hot reloading and fast refresh
  reactStrictMode: true,
  swcMinify: true,

  webpack: (config, { dev }) => {
    config.experiments = {
      ...config.experiments,
      topLevelAwait: true,
    };

    // Enable hot reloading for development
    if (dev) {
      config.watchOptions = {
        poll: 1000,
        aggregateTimeout: 300,
      };
    }

    return config;
  },
};

module.exports = nextConfig;
