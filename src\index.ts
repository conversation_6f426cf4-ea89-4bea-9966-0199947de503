import { logger } from '@/utils/logger';
import { BitunixDataCollector, DataCollectorConfig } from '@/services/BitunixDataCollector';
import { config } from '@/config/config';
import { BitunixConfig, KlineInterval } from '@/types/bitunix';

async function main(): Promise<void> {
  try {
    logger.info('Starting Bitunix Data Collector...');
    
    if (!config.bitunix.apiKey || !config.bitunix.secretKey) {
      throw new Error('Missing required API credentials. Please check your .env file.');
    }

    const bitunixConfig: BitunixConfig = {
      apiKey: config.bitunix.apiKey,
      secretKey: config.bitunix.secretKey,
      baseUrl: config.bitunix.baseUrl,
      wsUrl: config.bitunix.wsUrl,
    };

    const collectorConfig: DataCollectorConfig = {
      symbol: config.dataCollection.symbol,
      klineIntervals: config.dataCollection.klineIntervals as KlineInterval[],
      depthLimit: config.dataCollection.depthLimit,
      restDataInterval: config.app.dataCollectionInterval,
      depthCollectionInterval: config.app.depthCollectionInterval,
      klineCollectionInterval: config.app.klineCollectionInterval,
      enableWebSocket: config.dataCollection.enableWebSocket,
      enableRestApi: config.dataCollection.enableRestApi,
    };

    const dataCollector = new BitunixDataCollector(
      bitunixConfig,
      collectorConfig,
      config.dataCollection.storageDir
    );

    dataCollector.on('started', () => {
      logger.info('Data collection started successfully');
    });

    dataCollector.on('stopped', () => {
      logger.info('Data collection stopped');
    });

    dataCollector.on('error', (error: Error) => {
      logger.error('Data collector error:', error);
    });

    dataCollector.on('dataCollected', (type: string) => {
      logger.debug('Data collected:', { type, symbol: collectorConfig.symbol });
    });

    await dataCollector.start();

    logger.info('Bitunix Data Collector started successfully', {
      symbol: collectorConfig.symbol,
      intervals: collectorConfig.klineIntervals,
      websocket: collectorConfig.enableWebSocket,
      restApi: collectorConfig.enableRestApi,
    });

    setInterval(async () => {
      const status = dataCollector.getStatus();
      const storageStats = await dataCollector.getStorageStats();

      logger.info('System status:', {
        running: status.isRunning,
        wsConnected: status.wsConnected,
        rateLimitRemaining: status.rateLimitInfo.requestsRemaining,
        totalFiles: storageStats.totalFiles,
        totalSizeMB: Math.round(storageStats.totalSize / 1024 / 1024 * 100) / 100,
      });
    }, 60000);
    
    process.on('SIGINT', async () => {
      logger.info('Received SIGINT, shutting down gracefully...');
      await dataCollector.stop();
      process.exit(0);
    });

    process.on('SIGTERM', async () => {
      logger.info('Received SIGTERM, shutting down gracefully...');
      await dataCollector.stop();
      process.exit(0);
    });

  } catch (error) {
    logger.error('Failed to start Bitunix Data Collector:', error);
    process.exit(1);
  }
}


if (require.main === module) {
  main().catch(error => {
    logger.error('Unhandled error in main:', error);
    process.exit(1);
  });
}

export { main };
