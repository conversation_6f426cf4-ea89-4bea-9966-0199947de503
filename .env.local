# Bitunix API Configuration
BITUNIX_API_BASE_URL=https://fapi.bitunix.com
BITUNIX_WS_URL=wss://fapi.bitunix.com/public/
BITUNIX_API_KEY=854c8e977e4fbd3d6580a777922953dc
BITUNIX_SECRET_KEY=9b8f5106f76011329887d08142a86eab

# Application Configuration
NODE_ENV=development
LOG_LEVEL=info
DATA_COLLECTION_INTERVAL=60000

# Data Collection Configuration
TRADING_SYMBOL=ETHUSDT
KLINE_INTERVALS=1m,3m,5m,15m
DEPTH_LIMIT=50
ENABLE_WEBSOCKET=true
ENABLE_REST_API=true
STORAGE_DIR=./data

# Optimized Collection Intervals (milliseconds)
# Order book updates every 1 second for real-time trading data
DEPTH_COLLECTION_INTERVAL=1000
# Kline updates every 5 seconds (4 intervals = 4 requests every 5 seconds)
KLINE_COLLECTION_INTERVAL=5000

# Rate Limiting (10 req/sec/ip as per Bitunix documentation)
RATE_LIMIT_REQUESTS_PER_SECOND=10
RATE_LIMIT_BURST=5